import { createFileRoute } from '@tanstack/react-router';
import { CouponList } from '@/components/coupon-list';
import { Badge } from '@/components/ui/badge';
import { useState } from 'react';

export const Route = createFileRoute('/coupons')({
  component: CouponsPage,
});

function CouponsPage() {
  const [selectedCategory, setSelectedCategory] = useState('');

  const categories = [
    { value: '', label: 'Tất cả' },
    { value: 'fashion', label: 'Thời trang' },
    { value: 'electronics', label: 'Điện tử' },
    { value: 'home', label: 'Gia dụng' },
    { value: 'beauty', label: 'Làm đẹp' },
    { value: 'sports', label: 'Thể thao' },
    { value: 'books', label: 'Sách' },
  ];

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header Section */}
      <div className='text-center mb-8'>
        <h1 className='text-4xl font-bold text-gray-900 mb-4'>
          🎫 Mã Giảm Giá <PERSON>ee
        </h1>
        <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
          Tìm kiếm và sử dụng các mã giảm giá Shopee mới nhất để tiết kiệm tối
          đa khi mua sắm
        </p>
      </div>

      {/* Category Tabs */}
      <div className='flex flex-wrap gap-2 justify-center mb-8'>
        {categories.map(category => (
          <Badge
            key={category.value}
            variant={selectedCategory === category.value ? 'default' : 'secondary'}
            className='px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors'
            onClick={() => setSelectedCategory(category.value)}
          >
            {category.label}
          </Badge>
        ))}
      </div>

      {/* Real Coupons List from AccessTrade API */}
      <CouponList
        category={selectedCategory}
        merchant="shopee"
        sortBy="discount"
        showFilters={true}
        showPagination={true}
        pageSize={12}
        className="mt-6"
      />
    </div>
  );
}
