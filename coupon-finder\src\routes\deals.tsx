import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Star,
  Clock,
  TrendingUp,
  ShoppingCart,
  ExternalLink,
  Users,
  Loader2,
  AlertCircle,
} from 'lucide-react';
import { useDealsData } from '@/hooks/use-deals-data';
import type { TopSellingProduct, BestDeal } from '@/hooks/use-deals-data';

export const Route = createFileRoute('/deals')({
  component: DealsPage,
});

function DealsPage() {
  const [activeTab, setActiveTab] = useState('Tất cả')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(12)

  const { topSelling, bestDeals, isLoading, isError, error } = useDealsData()

  // Categories for tabs
  const categories = [
    'Tất cả',
    '<PERSON><PERSON> chạy',
    'Giá sốc',
  ]

  // Filter data based on active tab
  const getFilteredData = () => {
    switch (activeTab) {
      case 'Bán chạy':
        return topSelling.data || []
      case 'Giá sốc':
        return bestDeals.data || []
      default: // Tất cả
        return [
          ...(topSelling.data || []),
          ...(bestDeals.data || [])
        ]
    }
  }

  const filteredData = getFilteredData()
  const totalPages = Math.ceil(filteredData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage)

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    setCurrentPage(1) // Reset to first page when changing tabs
  }

  const handleLoadMore = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1)
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4 text-primary' />
            <p className='text-lg text-gray-600'>Đang tải deals hot nhất...</p>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (isError) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <AlertCircle className='h-8 w-8 mx-auto mb-4 text-red-500' />
            <p className='text-lg text-gray-600 mb-4'>Có lỗi xảy ra khi tải dữ liệu</p>
            <p className='text-sm text-gray-500'>{error?.message}</p>
            <Button
              onClick={() => window.location.reload()}
              className='mt-4'
            >
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header Section */}
      <div className='text-center mb-8'>
        <h1 className='text-4xl font-bold text-gray-900 mb-4'>
          🔥 Top Deals Hôm Nay
        </h1>
        <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
          Khám phá những deal hot nhất, sản phẩm bán chạy và ưu đãi có thời hạn
        </p>
      </div>

      {/* Category Tabs */}
      <div className='flex flex-wrap gap-2 justify-center mb-8'>
        {categories.map(category => (
          <Badge
            key={category}
            variant={category === activeTab ? 'default' : 'secondary'}
            className='px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors'
            onClick={() => handleTabChange(category)}
          >
            {category}
          </Badge>
        ))}
      </div>

      {/* Dynamic Content Based on Active Tab */}
      {activeTab === 'Tất cả' && (
        <>
          {/* Top Selling Section */}
          <div className='mb-8'>
            <div className='flex items-center gap-2 mb-4'>
              <TrendingUp className='h-6 w-6 text-green-500' />
              <h2 className='text-2xl font-bold text-gray-900'>
                Sản Phẩm Bán Chạy
              </h2>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {topSelling.data?.slice(0, 3).map((product, index) => (
                <TopSellingCard
                  key={product.id}
                  product={product}
                  rank={index + 1}
                />
              )) || (
                <div className='col-span-full text-center py-8'>
                  <p className='text-gray-500'>Không có sản phẩm bán chạy</p>
                </div>
              )}
            </div>
          </div>

          {/* Best Deals Section */}
          <div className='mb-8'>
            <h2 className='text-2xl font-bold text-gray-900 mb-4'>
              Deal Tốt Nhất Hôm Nay
            </h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
              {bestDeals.data?.slice(0, 8).map(product => (
                <DealCard key={product.id} product={product} />
              )) || (
                <div className='col-span-full text-center py-8'>
                  <p className='text-gray-500'>Không có deal tốt nhất</p>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {/* Filtered Content for Specific Tabs */}
      {activeTab !== 'Tất cả' && (
        <div className='mb-8'>
          <h2 className='text-2xl font-bold text-gray-900 mb-4'>
            {activeTab === 'Bán chạy' && (
              <div className='flex items-center gap-2'>
                <TrendingUp className='h-6 w-6 text-green-500' />
                Sản Phẩm Bán Chạy
              </div>
            )}
            {activeTab === 'Giá sốc' && activeTab}
          </h2>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
            {paginatedData.length > 0 ? (
              paginatedData.map((item: any, index: number) => {
                if (activeTab === 'Bán chạy') {
                  return <TopSellingCard key={item.id} product={item} rank={index + 1} />
                } else {
                  return <DealCard key={item.id} product={item} />
                }
              })
            ) : (
              <div className='col-span-full text-center py-8'>
                <p className='text-gray-500'>Không có dữ liệu cho {activeTab}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Load More Button */}
      {filteredData.length > itemsPerPage && currentPage < totalPages && (
        <div className='text-center mt-8'>
          <Button
            variant='outline'
            size='lg'
            onClick={handleLoadMore}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                Đang tải...
              </>
            ) : (
              `Xem thêm deals (${currentPage}/${totalPages})`
            )}
          </Button>
        </div>
      )}

      {/* Pagination Info */}
      {filteredData.length > 0 && (
        <div className='text-center mt-4 text-sm text-gray-600'>
          Hiển thị {Math.min(startIndex + 1, filteredData.length)} - {Math.min(startIndex + itemsPerPage, filteredData.length)} trong tổng số {filteredData.length} deals
        </div>
      )}
    </div>
  );
}



function TopSellingCard({ product, rank }: { product: TopSellingProduct; rank: number }) {
  const handleBuyNow = () => {
    if (product.affiliateLink && product.affiliateLink !== '#') {
      window.open(product.affiliateLink, '_blank', 'noopener,noreferrer')
    }
  }

  const handleViewProduct = () => {
    if (product.affiliateLink && product.affiliateLink !== '#') {
      window.open(product.affiliateLink, '_blank', 'noopener,noreferrer')
    }
  }
  return (
    <Card className='hover:shadow-lg transition-shadow duration-200'>
      <CardHeader className='pb-3'>
        <div className='flex gap-3'>
          <div className='relative'>
            <img
              src={product.image}
              alt={product.name}
              className='w-20 h-20 object-cover rounded-lg'
            />
            <div className='absolute -top-2 -left-2 bg-yellow-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold'>
              {rank}
            </div>
          </div>
          <div className='flex-1'>
            <CardTitle className='text-base font-semibold text-gray-900 line-clamp-2'>
              {product.name}
            </CardTitle>
            <CardDescription className='text-sm text-gray-600 mt-1'>
              {product.store}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className='pt-0'>
        <div className='space-y-3'>
          {/* Price and Rating */}
          <div className='flex items-center justify-between'>
            <span className='text-lg font-bold text-red-600'>
              {product.price.toLocaleString('vi-VN')}đ
            </span>
            <div className='flex items-center gap-1'>
              <Star className='h-4 w-4 fill-yellow-400 text-yellow-400' />
              <span className='text-sm font-medium'>{product.rating}</span>
            </div>
          </div>

          {/* Sales Info */}
          <div className='flex items-center gap-2 text-sm text-gray-600'>
            <Users className='h-4 w-4' />
            <span>{product.monthlySales.toLocaleString('vi-VN')} đã bán</span>
          </div>

          {/* Action Buttons */}
          <div className='flex gap-2'>
            <Button
              className='flex-1'
              size='sm'
              onClick={handleBuyNow}
              disabled={!product.affiliateLink || product.affiliateLink === '#'}
            >
              <ShoppingCart className='h-4 w-4 mr-2' />
              Mua ngay
            </Button>
            <Button
              variant='outline'
              size='sm'
              className='px-3'
              onClick={handleViewProduct}
              disabled={!product.affiliateLink || product.affiliateLink === '#'}
            >
              <ExternalLink className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function DealCard({ product }: { product: BestDeal }) {
  const handleBuyNow = () => {
    if (product.affiliateLink && product.affiliateLink !== '#') {
      window.open(product.affiliateLink, '_blank', 'noopener,noreferrer')
    }
  }

  const handleViewProduct = () => {
    if (product.affiliateLink && product.affiliateLink !== '#') {
      window.open(product.affiliateLink, '_blank', 'noopener,noreferrer')
    }
  }

  // Validate discount để đảm bảo hiển thị hợp lý
  const displayDiscount = () => {
    if (!product.discount || product.discount < 0 || product.discount > 100) {
      return 5 // Fallback discount
    }
    return Math.round(product.discount)
  }

  return (
    <Card className='hover:shadow-lg transition-shadow duration-200'>
      <CardHeader className='pb-3'>
        <div className='relative'>
          <img
            src={product.image}
            alt={product.name}
            className='w-full h-48 object-cover rounded-lg mb-3'
          />
          <Badge variant='destructive' className='absolute top-2 right-2'>
            -{displayDiscount()}%
          </Badge>
          {product.isLimitedTime && (
            <div className='absolute top-2 left-2 bg-orange-500 text-white px-2 py-1 rounded text-xs font-bold'>
              CÓ HẠN
            </div>
          )}
        </div>
        <CardTitle className='text-lg font-semibold text-gray-900 line-clamp-2'>
          {product.name}
        </CardTitle>
        <CardDescription className='text-sm text-gray-600'>
          {product.store}
        </CardDescription>
      </CardHeader>

      <CardContent className='pt-0'>
        <div className='space-y-3'>
          {/* Price */}
          <div className='flex items-center gap-2'>
            <span className='text-xl font-bold text-red-600'>
              {product.price.toLocaleString('vi-VN')}đ
            </span>
            {product.originalPrice && (
              <span className='text-sm text-gray-500 line-through'>
                {product.originalPrice.toLocaleString('vi-VN')}đ
              </span>
            )}
          </div>

          {/* Rating and Reviews */}
          <div className='flex items-center gap-2'>
            <div className='flex items-center'>
              <Star className='h-4 w-4 fill-yellow-400 text-yellow-400' />
              <span className='text-sm font-medium ml-1'>{product.rating}</span>
            </div>
            <span className='text-sm text-gray-500'>
              ({product.reviews} đánh giá)
            </span>
          </div>

          {/* Expiry Time */}
          {product.expiryTime && (
            <div className='flex items-center gap-2 text-sm text-orange-600'>
              <Clock className='h-4 w-4' />
              <span>Còn {product.expiryTime}</span>
            </div>
          )}

          {/* Action Buttons */}
          <div className='flex gap-2 pt-2'>
            <Button
              className='flex-1'
              onClick={handleBuyNow}
              disabled={!product.affiliateLink || product.affiliateLink === '#'}
            >
              <ShoppingCart className='h-4 w-4 mr-2' />
              Mua ngay
            </Button>
            <Button
              variant='outline'
              size='sm'
              className='px-3'
              onClick={handleViewProduct}
              disabled={!product.affiliateLink || product.affiliateLink === '#'}
            >
              <ExternalLink className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}


