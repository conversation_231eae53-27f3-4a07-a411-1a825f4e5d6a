import { Context, Next } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { AuthenticationError, AuthorizationError, RateLimitError } from './error-handler'

/**
 * JWT token verification middleware
 */
export function jwtAuth(options?: {
  secret?: string
  algorithms?: string[]
  skipPaths?: string[]
}) {
  return async (c: Context, next: Next) => {
    const { secret, skipPaths = [] } = options || {}
    
    // Skip authentication for certain paths
    if (skipPaths.some(path => c.req.path.startsWith(path))) {
      await next()
      return
    }

    const authHeader = c.req.header('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing or invalid authorization header')
    }

    const token = authHeader.substring(7)
    if (!token) {
      throw new AuthenticationError('Missing JWT token')
    }

    try {
      // Simple JWT verification (in production, use proper JWT library)
      const jwtSecret = secret || c.env?.JWT_SECRET
      if (!jwtSecret) {
        throw new Error('JWT secret not configured')
      }

      // For now, we'll do basic token validation
      // In production, implement proper JWT verification
      const payload = JSON.parse(atob(token.split('.')[1]))
      
      if (!payload.userId) {
        throw new AuthenticationError('Invalid token payload')
      }

      // Check token expiration
      if (payload.exp && Date.now() >= payload.exp * 1000) {
        throw new AuthenticationError('Token has expired')
      }

      // Store user info in context
      c.set('userId', payload.userId)
      c.set('userRole', payload.role || 'user')
      
      await next()
    } catch (error) {
      if (error instanceof AuthenticationError) {
        throw error
      }
      throw new AuthenticationError('Invalid or expired token')
    }
  }
}

/**
 * Role-based authorization middleware
 */
export function requireRole(allowedRoles: string | string[]) {
  return async (c: Context, next: Next) => {
    const userRole = c.get('userRole')
    
    if (!userRole) {
      throw new AuthenticationError('Authentication required')
    }

    const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles]
    
    if (!roles.includes(userRole)) {
      throw new AuthorizationError(`Access denied. Required role: ${roles.join(' or ')}`)
    }

    await next()
  }
}

/**
 * Admin-only middleware
 */
export function requireAdmin() {
  return requireRole('admin')
}

/**
 * API key authentication middleware
 */
export function apiKeyAuth(options?: {
  headerName?: string
  queryParam?: string
  validKeys?: string[]
}) {
  return async (c: Context, next: Next) => {
    const { 
      headerName = 'x-api-key', 
      queryParam = 'api_key',
      validKeys = []
    } = options || {}

    const apiKey = c.req.header(headerName) || c.req.query(queryParam)
    
    if (!apiKey) {
      throw new AuthenticationError('API key is required')
    }

    // Check against environment variable or provided valid keys
    const envApiKey = c.env?.API_KEY
    const isValid = envApiKey ? apiKey === envApiKey : validKeys.includes(apiKey)
    
    if (!isValid) {
      throw new AuthenticationError('Invalid API key')
    }

    await next()
  }
}

/**
 * IP whitelist middleware
 */
export function ipWhitelist(allowedIPs: string[]) {
  return async (c: Context, next: Next) => {
    const clientIP = c.req.header('x-forwarded-for') || 
                    c.req.header('x-real-ip') || 
                    c.req.header('cf-connecting-ip') ||
                    'unknown'

    if (!allowedIPs.includes(clientIP)) {
      throw new AuthorizationError(`Access denied for IP: ${clientIP}`)
    }

    await next()
  }
}

/**
 * CSRF protection middleware
 */
export function csrfProtection(options?: {
  tokenHeader?: string
  cookieName?: string
  skipMethods?: string[]
}) {
  return async (c: Context, next: Next) => {
    const { 
      tokenHeader = 'x-csrf-token',
      cookieName = 'csrf-token',
      skipMethods = ['GET', 'HEAD', 'OPTIONS']
    } = options || {}

    const method = c.req.method.toUpperCase()
    
    // Skip CSRF check for safe methods
    if (skipMethods.includes(method)) {
      await next()
      return
    }

    const token = c.req.header(tokenHeader)
    const cookieToken = c.req.header('cookie')?.match(new RegExp(`${cookieName}=([^;]+)`))?.[1]

    if (!token || !cookieToken || token !== cookieToken) {
      throw new HTTPException(403, {
        message: 'CSRF token mismatch'
      })
    }

    await next()
  }
}

/**
 * Request timeout middleware
 */
export function requestTimeout(timeoutMs: number = 30000) {
  return async (c: Context, next: Next) => {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new HTTPException(408, {
          message: 'Request timeout'
        }))
      }, timeoutMs)
    })

    try {
      await Promise.race([next(), timeoutPromise])
    } catch (error) {
      throw error
    }
  }
}

/**
 * User agent validation middleware
 */
export function validateUserAgent(options?: {
  blockedAgents?: string[]
  requiredAgents?: string[]
  allowEmpty?: boolean
}) {
  return async (c: Context, next: Next) => {
    const { 
      blockedAgents = [],
      requiredAgents = [],
      allowEmpty = true
    } = options || {}

    const userAgent = c.req.header('user-agent') || ''

    if (!allowEmpty && !userAgent) {
      throw new HTTPException(400, {
        message: 'User-Agent header is required'
      })
    }

    // Check blocked agents
    if (blockedAgents.some(agent => userAgent.toLowerCase().includes(agent.toLowerCase()))) {
      throw new HTTPException(403, {
        message: 'Access denied for this user agent'
      })
    }

    // Check required agents
    if (requiredAgents.length > 0 && 
        !requiredAgents.some(agent => userAgent.toLowerCase().includes(agent.toLowerCase()))) {
      throw new HTTPException(403, {
        message: 'Invalid user agent'
      })
    }

    await next()
  }
}

/**
 * Origin validation middleware
 */
export function validateOrigin(allowedOrigins: string[]) {
  return async (c: Context, next: Next) => {
    const origin = c.req.header('origin')
    
    if (origin && !allowedOrigins.includes(origin)) {
      throw new HTTPException(403, {
        message: 'Origin not allowed'
      })
    }

    await next()
  }
}

/**
 * Security headers middleware
 */
export function securityHeaders(options?: {
  contentSecurityPolicy?: string
  frameOptions?: string
  contentTypeOptions?: boolean
  referrerPolicy?: string
}) {
  return async (c: Context, next: Next) => {
    const {
      contentSecurityPolicy = "default-src 'self'",
      frameOptions = 'DENY',
      contentTypeOptions = true,
      referrerPolicy = 'strict-origin-when-cross-origin'
    } = options || {}

    await next()

    // Set security headers
    c.res.headers.set('Content-Security-Policy', contentSecurityPolicy)
    c.res.headers.set('X-Frame-Options', frameOptions)
    c.res.headers.set('X-Content-Type-Options', contentTypeOptions ? 'nosniff' : '')
    c.res.headers.set('Referrer-Policy', referrerPolicy)
    c.res.headers.set('X-XSS-Protection', '1; mode=block')
    c.res.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  }
}

/**
 * Request logging middleware for security events
 */
export function securityLogger() {
  return async (c: Context, next: Next) => {
    const startTime = Date.now()
    const requestId = c.get('requestId')
    
    try {
      await next()
    } catch (error) {
      // Log security-related errors
      if (error instanceof AuthenticationError || 
          error instanceof AuthorizationError ||
          error instanceof RateLimitError) {
        
        console.warn('Security event:', {
          type: error.name,
          message: error.message,
          requestId,
          path: c.req.path,
          method: c.req.method,
          userAgent: c.req.header('user-agent'),
          ip: c.req.header('x-forwarded-for') || c.req.header('cf-connecting-ip'),
          timestamp: new Date().toISOString(),
          duration: Date.now() - startTime,
        })
      }
      
      throw error
    }
  }
}
