import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { cache } from 'hono/cache'
import type { Bindings, Variables } from '../hono-app'
import { createAccessTradeClient, AccessTradeError } from '../../lib/accesstrade-client'

// Validation schemas
const searchProductsSchema = z.object({
  keyword: z.string().min(1, 'Keyword is required'),
  category: z.string().optional(),
  minPrice: z.string().transform(Number).pipe(z.number().min(0)).optional(),
  maxPrice: z.string().transform(Number).pipe(z.number().min(0)).optional(),
  sortBy: z.enum(['price', 'rating', 'discount', 'popularity']).default('popularity'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('20'),
  offset: z.string().transform(Number).pipe(z.number().min(0)).default('0'),
})

const getProductSchema = z.object({
  id: z.string().min(1, 'Product ID is required'),
})

const compareProductsSchema = z.object({
  productIds: z.array(z.string()).min(2, 'At least 2 products required').max(4, 'Maximum 4 products allowed'),
})

// Create products routes
export const productsRoutes = new Hono<{
  Bindings: Bindings
  Variables: Variables
}>()

// Cache middleware for product data
productsRoutes.use('/search', cache({
  cacheName: 'products-search',
  cacheControl: 'max-age=600', // 10 minutes
}))

// Get all products endpoint (default listing)
productsRoutes.get('/',
  zValidator('query', z.object({
    category: z.string().optional(),
    merchant: z.string().optional(),
    limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('20'),
    offset: z.string().transform(Number).pipe(z.number().min(0)).default('0'),
  })),
  async (c) => {
    try {
      const { category, merchant, limit, offset } = c.req.valid('query')

      // Generate cache key
      const cacheKey = `products:list:${JSON.stringify({ category, merchant, limit, offset })}`

      // Try to get from cache first (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Products retrieved from cache',
            data,
            cached: true,
          })
        }
      }

      // AccessTrade API call
      const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')

      const searchParams: any = {
        limit: limit || 20,
        offset: offset || 0,
      }

      if (merchant) {
        searchParams.merchant = merchant
      }

      if (category) {
        searchParams.category = category
      }

      const productsResponse = await accessTradeClient.searchProducts(searchParams)

      // Transform data
      const products = {
        products: productsResponse.data.map(product => ({
          id: product.product_id,
          name: product.name,
          description: product.desc,
          price: product.price,
          originalPrice: product.price,
          discount: product.discount,
          discountRate: product.discount_rate,
          imageUrl: product.image,
          affiliateLink: product.aff_link,
          merchant: product.merchant,
          category: product.cate || category || 'general',
          domain: product.domain,
          shopName: product.shop_name,
          sku: product.sku,
          updateTime: product.update_time,
        })).slice(0, limit),
        total: productsResponse.total || 0,
        limit,
        offset,
      }

      // Cache the results (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(products),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 600 }
        )
      }

      return c.json({
        success: true,
        message: 'Products retrieved successfully',
        data: products,
        cached: false,
      })

    } catch (error) {
      console.error('Get products error:', error)
      return c.json({
        error: 'Failed to get products',
        message: 'An error occurred while fetching products'
      }, 500)
    }
  }
)

// Search products endpoint
productsRoutes.get('/search',
  zValidator('query', searchProductsSchema),
  async (c) => {
    try {
      const params = c.req.valid('query')
      
      // Generate cache key
      const cacheKey = `products:search:${JSON.stringify(params)}`
      
      // Try to get from cache first
      if (c.env.ENABLE_CACHING === 'true') {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Products retrieved from cache',
            data,
            cached: true,
          })
        }
      }
      
      // AccessTrade API call - Search products from datafeeds
      const accessTradeClient = createAccessTradeClient(c.env.ACCESSTRADE_API_KEY)

      // Map category to domain if needed
      const searchParams: any = {
        limit: params.limit,
        page: Math.floor(params.offset / params.limit) + 1,
      }

      // Add price filters if provided
      if (params.minPrice) {
        searchParams.price_from = params.minPrice
      }
      if (params.maxPrice) {
        searchParams.price_to = params.maxPrice
      }

      // Add category filter
      if (params.category) {
        // Map category to merchant/domain
        const categoryMerchantMap: Record<string, string> = {
          'electronics': 'lazada',
          'fashion': 'shopee',
          'beauty': 'shopee',
          'home': 'lazada',
          'books': 'fahasa',
        }

        if (categoryMerchantMap[params.category]) {
          searchParams.campaign = categoryMerchantMap[params.category]
        }
      }

      const productsResponse = await accessTradeClient.searchProducts(searchParams)

      // Transform AccessTrade products to our format
      const products = {
        products: productsResponse.data.map(product => ({
          id: product.product_id,
          name: product.name,
          description: product.desc || product.name,
          price: parseFloat(product.discount) || parseFloat(product.price),
          originalPrice: parseFloat(product.price),
          discount: product.discount_rate || 0,
          rating: 4.5, // AccessTrade doesn't provide rating, use default
          reviewCount: 0, // AccessTrade doesn't provide review count
          imageUrl: product.image,
          merchant: product.campaign,
          category: product.cate,
          affiliateLink: product.aff_link,
          inStock: true,
          soldCount: 0, // AccessTrade doesn't provide sold count
          sku: product.sku,
          brand: product.brand,
          categoryName: product.category_name,
          domain: product.domain,
          discountAmount: product.discount_amount,
          statusDiscount: product.status_discount,
          updateTime: product.update_time,
        })),
        total: productsResponse.total || 0,
        limit: params.limit,
        offset: params.offset,
        filters: {
          keyword: params.keyword,
          category: params.category,
          priceRange: [params.minPrice, params.maxPrice],
          sortBy: params.sortBy,
          sortOrder: params.sortOrder,
        }
      }
      
      // Cache the results (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(products),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 3600 }
        )
      }

      // Track analytics (if env available)
      if (c.env?.ENABLE_ANALYTICS === 'true') {
        await trackProductSearch(c, {
          keyword: params.keyword,
          category: params.category,
          resultCount: products.products.length
        })
      }
      
      return c.json({
        success: true,
        message: 'Products retrieved successfully',
        data: products,
        cached: false,
      })
      
    } catch (error) {
      console.error('Search products error:', error)

      if (error instanceof AccessTradeError) {
        return c.json({
          error: 'AccessTrade API Error',
          message: error.message,
          status: error.status,
        }, error.status || 500)
      }

      return c.json({
        error: 'Search failed',
        message: 'An error occurred while searching for products'
      }, 500)
    }
  }
)

// Get product by ID
productsRoutes.get('/:id',
  zValidator('param', getProductSchema),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      
      // Generate cache key
      const cacheKey = `product:${id}`
      
      // Try to get from cache first (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Product retrieved from cache',
            data,
            cached: true,
          })
        }
      }

      // Mock AccessTrade API call (replace with actual implementation)
      const product = await getProductFromAccessTrade({
        id,
        apiKey: c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV',
      })
      
      if (!product) {
        return c.json({
          error: 'Product not found',
          message: 'The requested product was not found'
        }, 404)
      }
      
      // Cache the result (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(product),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 3600 }
        )
      }
      
      return c.json({
        success: true,
        message: 'Product retrieved successfully',
        data: product,
        cached: false,
      })
      
    } catch (error) {
      console.error('Get product error:', error)
      return c.json({
        error: 'Failed to get product',
        message: 'An error occurred while fetching product details'
      }, 500)
    }
  }
)

// Compare products endpoint
productsRoutes.post('/compare',
  zValidator('json', compareProductsSchema),
  async (c) => {
    try {
      const { productIds } = c.req.valid('json')
      
      // Generate cache key
      const cacheKey = `products:compare:${productIds.sort().join(',')}`
      
      // Try to get from cache first (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Product comparison retrieved from cache',
            data,
            cached: true,
          })
        }
      }

      // Fetch all products
      const products = await Promise.all(
        productIds.map(id => getProductFromAccessTrade({
          id,
          apiKey: c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV',
        }))
      )
      
      // Filter out null products
      const validProducts = products.filter(Boolean)
      
      if (validProducts.length < 2) {
        return c.json({
          error: 'Insufficient products',
          message: 'At least 2 valid products are required for comparison'
        }, 400)
      }
      
      // Generate comparison data
      const comparison = generateProductComparison(validProducts)
      
      // Cache the results (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(comparison),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 3600 }
        )
      }

      // Track analytics (if env available)
      if (c.env?.ENABLE_ANALYTICS === 'true') {
        await trackProductComparison(c, { productIds, productCount: validProducts.length })
      }
      
      return c.json({
        success: true,
        message: 'Product comparison generated successfully',
        data: comparison,
        cached: false,
      })
      
    } catch (error) {
      console.error('Compare products error:', error)
      return c.json({
        error: 'Comparison failed',
        message: 'An error occurred while comparing products'
      }, 500)
    }
  }
)

// Get top selling products
productsRoutes.get('/top-selling',
  async (c) => {
    try {
      const category = c.req.query('category')
      const limit = parseInt(c.req.query('limit') || '20')
      const offset = parseInt(c.req.query('offset') || '0')
      
      // Generate cache key
      const cacheKey = `products:top-selling:${category || 'all'}:${limit}:${offset}`
      
      // Try to get from cache first (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Top selling products retrieved from cache',
            data,
            cached: true,
          })
        }
      }

      // AccessTrade API call - Get top selling products
      const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')

      // Try without date filter first, then with date filter if needed
      let searchParams: any = {}

      // Add merchant filter based on category
      if (category) {
        const categoryMerchantMap: Record<string, string> = {
          'electronics': 'lazada',
          'fashion': 'shopee',
          'beauty': 'shopee',
          'home': 'lazada',
          'books': 'fahasa',
        }

        if (categoryMerchantMap[category]) {
          searchParams.merchant = categoryMerchantMap[category]
        }
      }

      const topProductsResponse = await accessTradeClient.getTopSellingProducts(searchParams)

      // Transform and paginate
      const products = {
        products: topProductsResponse.data.map(product => ({
          id: product.product_id,
          name: product.name,
          description: product.desc || product.name,
          price: parseFloat(product.discount) || parseFloat(product.price),
          originalPrice: parseFloat(product.price),
          discount: calculateDiscountPercentage(product.price, product.discount),
          rating: 4.5, // Default rating
          reviewCount: 0, // Not available in AccessTrade
          imageUrl: product.image,
          merchant: extractMerchantFromLink(product.link),
          category: product.category_name,
          affiliateLink: product.aff_link,
          inStock: true,
          soldCount: 0, // Not available in AccessTrade top products
          brand: product.brand,
          categoryId: product.category_id,
          productCategory: product.product_category,
        })).slice(offset, offset + limit), // Apply pagination
        total: topProductsResponse.total || 0,
        category,
        limit,
        offset,
      }
      
      // Cache the results (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(products),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 3600 }
        )
      }
      
      return c.json({
        success: true,
        message: 'Top selling products retrieved successfully',
        data: products,
        cached: false,
      })
      
    } catch (error) {
      console.error('Get top selling products error:', error)
      return c.json({
        error: 'Failed to get top selling products',
        message: 'An error occurred while fetching top selling products'
      }, 500)
    }
  }
)

// Helper functions (mock implementations - replace with actual AccessTrade API calls)
async function searchProductsFromAccessTrade(params: any) {
  // Mock implementation - replace with actual AccessTrade API integration
  return {
    products: [
      {
        id: '1',
        name: 'iPhone 15 Pro Max',
        description: 'Latest iPhone with advanced features',
        price: 29990000,
        originalPrice: 32990000,
        discount: 9,
        rating: 4.8,
        reviewCount: 1250,
        imageUrl: 'https://example.com/iphone15.jpg',
        merchant: 'shopee',
        category: 'electronics',
        affiliateLink: 'https://example.com/affiliate/iphone15',
        inStock: true,
        soldCount: 5000,
      }
    ],
    total: 1,
    limit: params.limit,
    offset: params.offset,
    filters: {
      keyword: params.keyword,
      category: params.category,
      priceRange: [params.minPrice, params.maxPrice],
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    }
  }
}

async function getProductFromAccessTrade(params: any) {
  // Mock implementation
  return {
    id: params.id,
    name: 'iPhone 15 Pro Max',
    description: 'Latest iPhone with advanced features',
    price: 29990000,
    originalPrice: 32990000,
    discount: 9,
    rating: 4.8,
    reviewCount: 1250,
    imageUrl: 'https://example.com/iphone15.jpg',
    merchant: 'shopee',
    category: 'electronics',
    affiliateLink: 'https://example.com/affiliate/iphone15',
    inStock: true,
    soldCount: 5000,
    specifications: {
      brand: 'Apple',
      model: 'iPhone 15 Pro Max',
      storage: '256GB',
      color: 'Natural Titanium',
    }
  }
}

async function getTopSellingProductsFromAccessTrade(params: any) {
  // Mock implementation
  return {
    products: [],
    total: 0,
    category: params.category,
    limit: params.limit,
    offset: params.offset,
  }
}

function generateProductComparison(products: any[]) {
  return {
    products,
    comparison: {
      priceRange: {
        min: Math.min(...products.map(p => p.price)),
        max: Math.max(...products.map(p => p.price)),
      },
      averageRating: products.reduce((sum, p) => sum + p.rating, 0) / products.length,
      bestValue: products.reduce((best, current) => 
        (current.discount > best.discount) ? current : best
      ),
      mostPopular: products.reduce((best, current) => 
        (current.soldCount > best.soldCount) ? current : best
      ),
    }
  }
}

async function trackProductSearch(c: any, data: any) {
  const timestamp = new Date().toISOString()
  const analyticsKey = `analytics:product-search:${timestamp}:${c.get('requestId')}`
  
  await c.env.ANALYTICS.put(analyticsKey, JSON.stringify({
    type: 'product_search',
    timestamp,
    requestId: c.get('requestId'),
    ...data,
  }))
}

async function trackProductComparison(c: any, data: any) {
  const timestamp = new Date().toISOString()
  const analyticsKey = `analytics:product-comparison:${timestamp}:${c.get('requestId')}`

  await c.env.ANALYTICS.put(analyticsKey, JSON.stringify({
    type: 'product_comparison',
    timestamp,
    requestId: c.get('requestId'),
    ...data,
  }))
}

// Helper functions for AccessTrade integration
function calculateDiscountPercentage(originalPrice: string, discountPrice: string): number {
  const original = parseFloat(originalPrice)
  const discount = parseFloat(discountPrice)

  if (original <= 0 || discount >= original) {
    return 0
  }

  return Math.round(((original - discount) / original) * 100)
}

function extractMerchantFromLink(link: string): string {
  try {
    const url = new URL(link)
    const hostname = url.hostname.toLowerCase()

    if (hostname.includes('shopee')) return 'shopee'
    if (hostname.includes('lazada')) return 'lazada'
    if (hostname.includes('tiki')) return 'tikivn'
    if (hostname.includes('sendo')) return 'sendo'
    if (hostname.includes('adayroi')) return 'adayroi'
    if (hostname.includes('fahasa')) return 'fahasa'

    return 'unknown'
  } catch {
    return 'unknown'
  }
}

export default productsRoutes
