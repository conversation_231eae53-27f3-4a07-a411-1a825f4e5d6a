/**
 * Test script để kiểm tra AccessTrade API
 * Chạy: node test-accesstrade-api.js
 */

const API_KEY = 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV';
const BASE_URL = 'https://api.accesstrade.vn/v1';

async function testAccessTradeAPI() {
  console.log('🔍 Testing AccessTrade API...\n');

  // Test các endpoints khác nhau
  const endpoints = [
    '/offers_informations',
    '/offers',
    '/campaigns',
    '/datafeeds',
    '/coupons',
    '/vouchers'
  ];

  for (const endpoint of endpoints) {
    console.log(`📡 Testing endpoint: ${endpoint}`);
    
    try {
      const url = new URL(`${BASE_URL}${endpoint}`);
      url.searchParams.append('status', '1');
      url.searchParams.append('limit', '5');

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Token ${API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ Success! Data type: ${Array.isArray(data) ? 'Array' : typeof data}`);
        
        if (Array.isArray(data)) {
          console.log(`   📊 Array length: ${data.length}`);
          if (data.length > 0) {
            console.log(`   🔍 First item keys: ${Object.keys(data[0]).join(', ')}`);
          }
        } else if (data && typeof data === 'object') {
          console.log(`   🔍 Object keys: ${Object.keys(data).join(', ')}`);
          if (data.data && Array.isArray(data.data)) {
            console.log(`   📊 Data array length: ${data.data.length}`);
            if (data.data.length > 0) {
              console.log(`   🔍 First data item keys: ${Object.keys(data.data[0]).join(', ')}`);
            }
          }
        }
        
        console.log(`   📄 Sample response:`, JSON.stringify(data, null, 2).substring(0, 500) + '...\n');
      } else {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText}\n`);
      }
    } catch (error) {
      console.log(`   💥 Exception: ${error.message}\n`);
    }
  }

  // Test với các parameters khác nhau
  console.log('🔍 Testing with different parameters...\n');
  
  const testParams = [
    { merchant: 'shopee' },
    { categories: 'fashion' },
    { coupon: 1 },
    { scope: 'all' },
    { domain: 'shopee.vn' }
  ];

  for (const params of testParams) {
    console.log(`📡 Testing /offers_informations with params:`, params);
    
    try {
      const url = new URL(`${BASE_URL}/offers_informations`);
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Token ${API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ Success! Data type: ${Array.isArray(data) ? 'Array' : typeof data}`);
        
        if (Array.isArray(data)) {
          console.log(`   📊 Array length: ${data.length}`);
        } else if (data && data.data && Array.isArray(data.data)) {
          console.log(`   📊 Data array length: ${data.data.length}`);
        }
      } else {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   💥 Exception: ${error.message}`);
    }
    
    console.log('');
  }
}

// Chạy test
testAccessTradeAPI().catch(console.error);
