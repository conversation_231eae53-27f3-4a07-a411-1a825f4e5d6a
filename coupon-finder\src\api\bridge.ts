/**
 * Bridge between TanStack Start and Hono API
 * 
 * This module provides the integration layer between TanStack Start's
 * API routes and the Hono application, enabling seamless request/response
 * handling and type safety.
 */

import { Context } from 'hono'
import type { AppType } from './hono-app'

/**
 * TanStack Start request interface
 */
export interface TanStackRequest {
  method: string
  url: string
  headers: Headers
  body?: ReadableStream | null
}

/**
 * TanStack Start response interface
 */
export interface TanStackResponse {
  status: number
  statusText: string
  headers: Headers
  body?: ReadableStream | null
}

/**
 * Bridge configuration options
 */
export interface BridgeOptions {
  basePath?: string
  timeout?: number
  enableLogging?: boolean
  enableMetrics?: boolean
  transformRequest?: (request: Request) => Request | Promise<Request>
  transformResponse?: (response: Response) => Response | Promise<Response>
}

/**
 * Request transformation utilities
 */
export class RequestTransformer {
  /**
   * Transform TanStack Start request to Hono-compatible request
   */
  static async transformToHono(request: Request, basePath: string = '/api'): Promise<Request> {
    const url = new URL(request.url)
    
    // Remove base path if present
    if (url.pathname.startsWith(basePath)) {
      url.pathname = url.pathname.substring(basePath.length) || '/'
    }
    
    // Ensure path starts with /
    if (!url.pathname.startsWith('/')) {
      url.pathname = '/' + url.pathname
    }
    
    // Create new request with transformed URL
    return new Request(url.toString(), {
      method: request.method,
      headers: request.headers,
      body: request.body,
      signal: request.signal,
    })
  }
  
  /**
   * Transform Hono response to TanStack Start-compatible response
   */
  static async transformFromHono(response: Response): Promise<Response> {
    // Add CORS headers if not present
    const headers = new Headers(response.headers)
    
    if (!headers.has('Access-Control-Allow-Origin')) {
      headers.set('Access-Control-Allow-Origin', '*')
    }
    
    if (!headers.has('Access-Control-Allow-Methods')) {
      headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    }
    
    if (!headers.has('Access-Control-Allow-Headers')) {
      headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
    }
    
    // Add cache control for API responses
    if (!headers.has('Cache-Control')) {
      headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    }
    
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers,
    })
  }
}

/**
 * Response utilities
 */
export class ResponseUtils {
  /**
   * Create JSON response
   */
  static json(data: any, status: number = 200, headers?: HeadersInit): Response {
    return new Response(JSON.stringify(data), {
      status,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    })
  }
  
  /**
   * Create error response
   */
  static error(message: string, status: number = 500, details?: any): Response {
    return ResponseUtils.json({
      error: true,
      message,
      details,
      timestamp: new Date().toISOString(),
    }, status)
  }
  
  /**
   * Create success response
   */
  static success(data: any, message?: string, status: number = 200): Response {
    return ResponseUtils.json({
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
    }, status)
  }
  
  /**
   * Create validation error response
   */
  static validationError(errors: any[], message: string = 'Validation failed'): Response {
    return ResponseUtils.json({
      error: true,
      message,
      errors,
      timestamp: new Date().toISOString(),
    }, 400)
  }
}

/**
 * Bridge metrics collector
 */
export class BridgeMetrics {
  private static metrics = {
    requests: 0,
    responses: 0,
    errors: 0,
    totalLatency: 0,
    avgLatency: 0,
  }
  
  static recordRequest(): void {
    this.metrics.requests++
  }
  
  static recordResponse(latency: number): void {
    this.metrics.responses++
    this.metrics.totalLatency += latency
    this.metrics.avgLatency = this.metrics.totalLatency / this.metrics.responses
  }
  
  static recordError(): void {
    this.metrics.errors++
  }
  
  static getMetrics() {
    return { ...this.metrics }
  }
  
  static reset(): void {
    this.metrics = {
      requests: 0,
      responses: 0,
      errors: 0,
      totalLatency: 0,
      avgLatency: 0,
    }
  }
}

/**
 * Main bridge class
 */
export class HonoBridge {
  private honoApp: any
  private options: BridgeOptions
  
  constructor(honoApp: any, options: BridgeOptions = {}) {
    this.honoApp = honoApp
    this.options = {
      basePath: '/api',
      timeout: 30000,
      enableLogging: true,
      enableMetrics: true,
      ...options,
    }
  }
  
  /**
   * Handle TanStack Start request through Hono
   */
  async handle(request: Request): Promise<Response> {
    const startTime = Date.now()
    
    try {
      if (this.options.enableMetrics) {
        BridgeMetrics.recordRequest()
      }
      
      if (this.options.enableLogging) {
        console.log(`[Bridge] ${request.method} ${request.url}`)
      }
      
      // Transform request for Hono
      let transformedRequest = await RequestTransformer.transformToHono(
        request, 
        this.options.basePath
      )
      
      // Apply custom request transformation if provided
      if (this.options.transformRequest) {
        transformedRequest = await this.options.transformRequest(transformedRequest)
      }
      
      // Create timeout promise
      const timeoutPromise = new Promise<Response>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Request timeout'))
        }, this.options.timeout)
      })
      
      // Execute Hono request with timeout
      const honoPromise = this.honoApp.fetch(transformedRequest)
      let response = await Promise.race([honoPromise, timeoutPromise])
      
      // Transform response from Hono
      response = await RequestTransformer.transformFromHono(response)
      
      // Apply custom response transformation if provided
      if (this.options.transformResponse) {
        response = await this.options.transformResponse(response)
      }
      
      const latency = Date.now() - startTime
      
      if (this.options.enableMetrics) {
        BridgeMetrics.recordResponse(latency)
      }
      
      if (this.options.enableLogging) {
        console.log(`[Bridge] ${response.status} ${request.method} ${request.url} (${latency}ms)`)
      }
      
      return response
      
    } catch (error) {
      const latency = Date.now() - startTime
      
      if (this.options.enableMetrics) {
        BridgeMetrics.recordError()
      }
      
      if (this.options.enableLogging) {
        console.error(`[Bridge] Error ${request.method} ${request.url} (${latency}ms):`, error)
      }
      
      // Handle timeout errors
      if (error instanceof Error && error.message === 'Request timeout') {
        return ResponseUtils.error('Request timeout', 408)
      }
      
      // Handle other errors
      return ResponseUtils.error(
        error instanceof Error ? error.message : 'Internal server error',
        500,
        this.options.enableLogging ? { stack: error instanceof Error ? error.stack : undefined } : undefined
      )
    }
  }
  
  /**
   * Create TanStack Start API route handler
   */
  createHandler() {
    return async (request: Request): Promise<Response> => {
      return this.handle(request)
    }
  }
  
  /**
   * Get bridge metrics
   */
  getMetrics() {
    return BridgeMetrics.getMetrics()
  }
  
  /**
   * Reset bridge metrics
   */
  resetMetrics() {
    BridgeMetrics.reset()
  }
}

/**
 * Utility function to create bridge instance
 */
export function createHonoBridge(honoApp: any, options?: BridgeOptions): HonoBridge {
  return new HonoBridge(honoApp, options)
}

/**
 * Utility function to create TanStack Start API handler
 */
export function createApiHandler(honoApp: any, options?: BridgeOptions) {
  const bridge = createHonoBridge(honoApp, options)
  return bridge.createHandler()
}

/**
 * Type-safe bridge for specific Hono app
 */
export function createTypedBridge<T extends AppType>(honoApp: T, options?: BridgeOptions) {
  return createHonoBridge(honoApp, options)
}

/**
 * Export types for external use
 */
export type { AppType }
