/**
 * Cloudflare Workers bindings middleware
 * 
 * This module provides middleware for integrating with Cloudflare Workers
 * bindings including D1, KV, R2, and other Cloudflare services.
 */

import { Context, Next } from 'hono'
import { DatabaseError } from './error-handler'

/**
 * KV namespace interface
 */
export interface KVOperations {
  get(key: string): Promise<string | null>
  put(key: string, value: string, options?: KVPutOptions): Promise<void>
  delete(key: string): Promise<void>
  list(options?: KVListOptions): Promise<KVListResult>
}

/**
 * KV put options
 */
export interface KVPutOptions {
  expirationTtl?: number
  expiration?: number
  metadata?: any
}

/**
 * KV list options
 */
export interface KVListOptions {
  prefix?: string
  limit?: number
  cursor?: string
}

/**
 * KV list result
 */
export interface KVListResult {
  keys: Array<{
    name: string
    expiration?: number
    metadata?: any
  }>
  list_complete: boolean
  cursor?: string
}

/**
 * R2 bucket interface
 */
export interface R2Operations {
  get(key: string): Promise<R2Object | null>
  put(key: string, value: ReadableStream | ArrayBuffer | string, options?: R2PutOptions): Promise<R2Object>
  delete(key: string): Promise<void>
  list(options?: R2ListOptions): Promise<R2Objects>
}

/**
 * R2 put options
 */
export interface R2PutOptions {
  httpMetadata?: R2HTTPMetadata
  customMetadata?: Record<string, string>
}

/**
 * R2 HTTP metadata
 */
export interface R2HTTPMetadata {
  contentType?: string
  contentLanguage?: string
  contentDisposition?: string
  contentEncoding?: string
  cacheControl?: string
  expires?: Date
}

/**
 * R2 list options
 */
export interface R2ListOptions {
  prefix?: string
  delimiter?: string
  cursor?: string
  include?: ('httpMetadata' | 'customMetadata')[]
  limit?: number
}

/**
 * R2 object
 */
export interface R2Object {
  key: string
  version: string
  size: number
  etag: string
  httpEtag: string
  uploaded: Date
  httpMetadata?: R2HTTPMetadata
  customMetadata?: Record<string, string>
  range?: R2Range
  body: ReadableStream
  bodyUsed: boolean
  arrayBuffer(): Promise<ArrayBuffer>
  text(): Promise<string>
  json<T = any>(): Promise<T>
  blob(): Promise<Blob>
}

/**
 * R2 objects list
 */
export interface R2Objects {
  objects: R2Object[]
  truncated: boolean
  cursor?: string
  delimitedPrefixes: string[]
}

/**
 * R2 range
 */
export interface R2Range {
  offset: number
  length: number
}

/**
 * Cloudflare bindings middleware
 */
export function cloudflareBindingsMiddleware() {
  return async (c: Context, next: Next) => {
    // Check if we're in development mode
    const isDevelopment = !c.env || Object.keys(c.env).length === 0 || c.env.NODE_ENV === 'development'

    if (isDevelopment) {
      console.log('Development mode: Cloudflare bindings not available')
      // Store mock bindings for development
      c.set('bindings', {
        DB: null,
        CACHE: null,
        ANALYTICS: null,
        CONFIG: null,
      })
    } else {
      // Store raw bindings in context for direct access
      c.set('bindings', {
        DB: c.env.DB,
        CACHE: c.env.CACHE,
        ANALYTICS: c.env.ANALYTICS,
        CONFIG: c.env.CONFIG,
      })
    }

    await next()
  }
}

/**
 * KV middleware for caching operations
 */
export function kvMiddleware(namespace: 'CACHE' | 'ANALYTICS' | 'CONFIG' = 'CACHE') {
  return async (c: Context, next: Next) => {
    const kv = c.env[namespace] as KVNamespace
    
    if (!kv) {
      console.warn(`KV namespace ${namespace} not available`)
      await next()
      return
    }

    // Create KV operations wrapper
    const kvOps: KVOperations = {
      async get(key: string): Promise<string | null> {
        try {
          return await kv.get(key)
        } catch (error) {
          console.error(`KV get error for key ${key}:`, error)
          return null
        }
      },

      async put(key: string, value: string, options?: KVPutOptions): Promise<void> {
        try {
          await kv.put(key, value, options)
        } catch (error) {
          console.error(`KV put error for key ${key}:`, error)
          throw error
        }
      },

      async delete(key: string): Promise<void> {
        try {
          await kv.delete(key)
        } catch (error) {
          console.error(`KV delete error for key ${key}:`, error)
          throw error
        }
      },

      async list(options?: KVListOptions): Promise<KVListResult> {
        try {
          return await kv.list(options)
        } catch (error) {
          console.error('KV list error:', error)
          throw error
        }
      },
    }

    // Store KV operations in context
    c.set(`kv${namespace}`, kvOps)
    c.set('kv', kvOps) // Default KV namespace

    await next()
  }
}

/**
 * R2 middleware for object storage operations
 */
export function r2Middleware(bucketName: string = 'DEFAULT') {
  return async (c: Context, next: Next) => {
    const bucket = c.env[bucketName] as R2Bucket
    
    if (!bucket) {
      console.warn(`R2 bucket ${bucketName} not available`)
      await next()
      return
    }

    // Create R2 operations wrapper
    const r2Ops: R2Operations = {
      async get(key: string): Promise<R2Object | null> {
        try {
          return await bucket.get(key)
        } catch (error) {
          console.error(`R2 get error for key ${key}:`, error)
          return null
        }
      },

      async put(key: string, value: ReadableStream | ArrayBuffer | string, options?: R2PutOptions): Promise<R2Object> {
        try {
          return await bucket.put(key, value, options)
        } catch (error) {
          console.error(`R2 put error for key ${key}:`, error)
          throw error
        }
      },

      async delete(key: string): Promise<void> {
        try {
          await bucket.delete(key)
        } catch (error) {
          console.error(`R2 delete error for key ${key}:`, error)
          throw error
        }
      },

      async list(options?: R2ListOptions): Promise<R2Objects> {
        try {
          return await bucket.list(options)
        } catch (error) {
          console.error('R2 list error:', error)
          throw error
        }
      },
    }

    // Store R2 operations in context
    c.set('r2', r2Ops)

    await next()
  }
}

/**
 * Environment variables middleware
 */
export function envMiddleware() {
  return async (c: Context, next: Next) => {
    // Create environment helper
    const env = {
      get(key: string, defaultValue?: string): string | undefined {
        return c.env[key] || defaultValue
      },

      getRequired(key: string): string {
        const value = c.env[key]
        if (!value) {
          throw new Error(`Required environment variable ${key} is not set`)
        }
        return value
      },

      getBoolean(key: string, defaultValue: boolean = false): boolean {
        const value = c.env[key]
        if (!value) return defaultValue
        return value.toLowerCase() === 'true'
      },

      getNumber(key: string, defaultValue?: number): number | undefined {
        const value = c.env[key]
        if (!value) return defaultValue
        const num = parseInt(value, 10)
        return isNaN(num) ? defaultValue : num
      },

      isDevelopment(): boolean {
        return this.get('NODE_ENV') === 'development'
      },

      isProduction(): boolean {
        return this.get('NODE_ENV') === 'production'
      },

      isTest(): boolean {
        return this.get('NODE_ENV') === 'test'
      },
    }

    // Store environment helper in context
    c.set('env', env)

    await next()
  }
}

/**
 * Caching middleware using KV
 */
export function cacheMiddleware(options?: {
  ttl?: number
  keyPrefix?: string
  skipMethods?: string[]
  skipPaths?: string[]
}) {
  const {
    ttl = 300, // 5 minutes default
    keyPrefix = 'cache:',
    skipMethods = ['POST', 'PUT', 'DELETE', 'PATCH'],
    skipPaths = []
  } = options || {}

  return async (c: Context, next: Next) => {
    const method = c.req.method.toUpperCase()
    const path = c.req.path

    // Skip caching for certain methods and paths
    if (skipMethods.includes(method) || skipPaths.some(p => path.startsWith(p))) {
      await next()
      return
    }

    const kv = c.get('kv') as KVOperations
    if (!kv) {
      await next()
      return
    }

    // Generate cache key
    const url = new URL(c.req.url)
    const cacheKey = `${keyPrefix}${method}:${url.pathname}${url.search}`

    try {
      // Try to get cached response
      const cached = await kv.get(cacheKey)
      if (cached) {
        const cachedResponse = JSON.parse(cached)
        return new Response(cachedResponse.body, {
          status: cachedResponse.status,
          headers: {
            ...cachedResponse.headers,
            'X-Cache': 'HIT',
            'X-Cache-Key': cacheKey,
          },
        })
      }

      // Execute request
      await next()

      // Cache successful responses
      if (c.res.status >= 200 && c.res.status < 300) {
        const responseBody = await c.res.text()
        const responseHeaders: Record<string, string> = {}
        
        c.res.headers.forEach((value, key) => {
          responseHeaders[key] = value
        })

        const cacheData = {
          body: responseBody,
          status: c.res.status,
          headers: responseHeaders,
        }

        // Store in cache
        await kv.put(cacheKey, JSON.stringify(cacheData), { expirationTtl: ttl })

        // Update response with cache headers
        c.res.headers.set('X-Cache', 'MISS')
        c.res.headers.set('X-Cache-Key', cacheKey)
        c.res.headers.set('X-Cache-TTL', ttl.toString())
      }
    } catch (error) {
      console.error('Cache middleware error:', error)
      // Continue without caching on error
      await next()
    }
  }
}

/**
 * Analytics middleware using KV
 */
export function analyticsMiddleware() {
  return async (c: Context, next: Next) => {
    const startTime = Date.now()
    const requestId = c.get('requestId')

    try {
      await next()
    } finally {
      // Record analytics data
      const endTime = Date.now()
      const duration = endTime - startTime

      const analyticsData = {
        requestId,
        method: c.req.method,
        path: c.req.path,
        status: c.res.status,
        duration,
        timestamp: new Date().toISOString(),
        userAgent: c.req.header('user-agent'),
        ip: c.req.header('cf-connecting-ip') || c.req.header('x-forwarded-for'),
        country: c.req.header('cf-ipcountry'),
        userId: c.get('userId'),
      }

      // Store analytics data in KV (fire and forget)
      const analyticsKV = c.get('kvANALYTICS') as KVOperations
      if (analyticsKV) {
        const key = `analytics:${Date.now()}:${requestId}`
        analyticsKV.put(key, JSON.stringify(analyticsData), { expirationTtl: 86400 * 30 }) // 30 days
          .catch(error => console.error('Analytics storage error:', error))
      }
    }
  }
}
