import { hc } from 'hono/client'
import type { AppType } from './hono-app'

/**
 * Enhanced type-safe Hono RPC client configuration
 */
export interface HonoClientOptions {
  headers?: Record<string, string>
  credentials?: RequestCredentials
  timeout?: number
  retries?: number
  retryDelay?: number
  onError?: (error: Error, attempt: number) => void
  onSuccess?: (response: Response) => void
  interceptors?: {
    request?: (request: Request) => Request | Promise<Request>
    response?: (response: Response) => Response | Promise<Response>
  }
}

/**
 * Create enhanced type-safe Hono RPC client with error handling and retries
 */
export const createHonoClient = (baseUrl: string, options?: HonoClientOptions) => {
  const {
    headers = {},
    credentials = 'include',
    timeout = 30000,
    retries = 3,
    retryDelay = 1000,
    onError,
    onSuccess,
    interceptors,
  } = options || {}

  // Create base client
  const baseClient = hc<AppType>(baseUrl, {
    init: {
      credentials,
      signal: AbortSignal.timeout(timeout),
    },
    headers: {
      'Content-Type': 'application/json',
      'X-Client-Version': '1.0.0',
      'X-Client-Type': 'hono-rpc',
      ...headers,
    },
  })

  // Enhanced client with retry logic and interceptors
  const enhancedClient = new Proxy(baseClient, {
    get(target, prop, receiver) {
      const original = Reflect.get(target, prop, receiver)

      // If it's a function (API call), wrap it with retry logic
      if (typeof original === 'function') {
        return async (...args: any[]) => {
          let lastError: Error | null = null

          for (let attempt = 1; attempt <= retries; attempt++) {
            try {
              // Apply request interceptor if provided
              if (interceptors?.request) {
                // Note: This is a simplified implementation
                // In practice, you'd need to intercept at the fetch level
              }

              const result = await original.apply(target, args)

              // Apply response interceptor if provided
              if (interceptors?.response && result instanceof Response) {
                return await interceptors.response(result)
              }

              // Call success callback
              if (onSuccess && result instanceof Response) {
                onSuccess(result)
              }

              return result
            } catch (error) {
              lastError = error instanceof Error ? error : new Error(String(error))

              // Call error callback
              if (onError) {
                onError(lastError, attempt)
              }

              // If this is the last attempt, throw the error
              if (attempt === retries) {
                throw lastError
              }

              // Wait before retrying
              await new Promise(resolve => setTimeout(resolve, retryDelay * attempt))
            }
          }

          throw lastError
        }
      }

      return original
    }
  })

  return enhancedClient as ReturnType<typeof hc<AppType>>
}

// Default client instance for browser usage - using official Hono client
export const honoClient = hc<AppType>(
  typeof window !== 'undefined'
    ? window.location.origin + '/api'
    : 'http://localhost:3001/api',
  {
    init: {
      credentials: 'include',
    },
  }
)



// Server-side client factory for TanStack Start API routes
export const createServerHonoClient = (request: Request) => {
  const url = new URL(request.url)
  const baseUrl = `${url.protocol}//${url.host}`
  
  return createHonoClient(baseUrl, {
    headers: {
      'User-Agent': request.headers.get('User-Agent') || '',
      'X-Forwarded-For': request.headers.get('X-Forwarded-For') || '',
      'X-Real-IP': request.headers.get('X-Real-IP') || '',
    },
  })
}

// Utility functions for common API operations
export const api = {
  // Authentication
  auth: {
    login: async (email: string, password: string) => {
      const res = await honoClient.auth.login.$post({
        json: { email, password }
      })
      return res.json()
    },

    register: async (email: string, password: string, name?: string) => {
      const res = await honoClient.auth.register.$post({
        json: { email, password, name }
      })
      return res.json()
    },

    getMe: async (token: string) => {
      const client = createHonoClient(
        typeof window !== 'undefined'
          ? window.location.origin
          : 'http://localhost:3001',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )
      const res = await client.auth.me.$get()
      return res.json()
    },

    logout: async (token: string) => {
      const client = createHonoClient(
        typeof window !== 'undefined'
          ? window.location.origin
          : 'http://localhost:3001',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )
      const res = await client.auth.logout.$post()
      return res.json()
    },
  },
  
  // Coupons
  coupons: {
    search: async (params: {
      url?: string
      keyword?: string
      category?: string
      merchant?: string
      sortBy?: 'discount' | 'expiry' | 'popularity' | 'newest'
      limit?: number
      offset?: number
    }) => {
      const queryParams: Record<string, string | undefined> = {
        limit: params.limit?.toString() || '20',
        offset: params.offset?.toString() || '0',
        merchant: params.merchant || 'shopee',
      }

      // Only add optional parameters if they have values
      if (params.url) queryParams.url = params.url
      if (params.keyword) queryParams.keyword = params.keyword
      if (params.category) queryParams.category = params.category
      if (params.sortBy) queryParams.sortBy = params.sortBy

      const res = await honoClient.coupons.search.$get({
        query: queryParams as any
      })
      return res.json()
    },
    
    getById: async (id: string) => {
      const res = await honoClient.coupons[':id'].$get({
        param: { id }
      })
      return res.json()
    },

    getByCategory: async (category: string, limit = 20, offset = 0) => {
      const res = await honoClient.coupons.category[':category'].$get({
        param: { category },
        query: {
          limit: limit.toString(),
          offset: offset.toString(),
        }
      })
      return res.json()
    },

    track: async (id: string) => {
      const res = await honoClient.coupons[':id'].track.$post({
        param: { id }
      })
      return res.json()
    },

    getHot: async (params: {
      limit?: number
      date?: 1 | 2 // 1: tuần, 2: tháng
    } = {}) => {
      const res = await honoClient.coupons.hot.$get({
        query: {
          limit: params.limit?.toString() || '20',
          date: params.date?.toString() || '1',
        }
      })
      return res.json()
    },
  },
  
  // Products
  products: {
    getAll: async (params: {
      category?: string
      merchant?: string
      limit?: number
      offset?: number
    } = {}) => {
      const res = await honoClient.products.$get({
        query: {
          category: params.category,
          merchant: params.merchant,
          limit: params.limit?.toString() || '20',
          offset: params.offset?.toString() || '0',
        }
      })
      return res.json()
    },

    search: async (params: {
      keyword: string
      category?: string
      minPrice?: number
      maxPrice?: number
      sortBy?: 'price' | 'rating' | 'discount' | 'popularity'
      sortOrder?: 'asc' | 'desc'
      limit?: number
      offset?: number
    }) => {
      const res = await honoClient.products.search.$get({
        query: {
          keyword: params.keyword,
          category: params.category,
          minPrice: params.minPrice?.toString(),
          maxPrice: params.maxPrice?.toString(),
          sortBy: params.sortBy || 'popularity',
          sortOrder: params.sortOrder || 'desc',
          limit: params.limit?.toString() || '20',
          offset: params.offset?.toString() || '0',
        }
      })
      return res.json()
    },

    getById: async (id: string) => {
      const res = await honoClient.products[':id'].$get({
        param: { id }
      })
      return res.json()
    },

    compare: async (productIds: string[]) => {
      const res = await honoClient.products.compare.$post({
        json: { productIds }
      })
      return res.json()
    },

    getTopSelling: async (category?: string, limit = 20, offset = 0) => {
      const res = await honoClient.products['top-selling'].$get({
        query: {
          category,
          limit: limit.toString(),
          offset: offset.toString(),
        }
      })
      return res.json()
    },
  },
  
  // Campaigns
  campaigns: {
    getAll: async (params: {
      category?: string
      merchant?: string
      status?: 'active' | 'upcoming' | 'expired'
      limit?: number
      offset?: number
    } = {}) => {
      const res = await honoClient.campaigns.$get({
        query: {
          category: params.category,
          merchant: params.merchant,
          status: params.status || 'active',
          limit: params.limit?.toString() || '20',
          offset: params.offset?.toString() || '0',
        }
      })
      return res.json()
    },

    getById: async (id: string) => {
      const res = await honoClient.campaigns[':id'].$get({
        param: { id }
      })
      return res.json()
    },
  },
  
  // Analytics (admin only)
  analytics: {
    getAll: async (token: string, params: {
      type?: 'clicks' | 'searches' | 'conversions' | 'all'
      startDate?: string
      endDate?: string
      limit?: number
    } = {}) => {
      const client = createHonoClient(
        typeof window !== 'undefined'
          ? window.location.origin
          : 'http://localhost:3001',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )
      const res = await client.analytics.$get({
        query: {
          type: params.type || 'all',
          startDate: params.startDate,
          endDate: params.endDate,
          limit: params.limit?.toString() || '100',
        }
      })
      return res.json()
    },

    getSummary: async (token: string) => {
      const client = createHonoClient(
        typeof window !== 'undefined'
          ? window.location.origin
          : 'http://localhost:3001',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )
      const res = await client.analytics.summary.$get()
      return res.json()
    },
  },
  
  // Admin (admin only)
  admin: {
    getFeaturedProducts: async (token: string) => {
      const client = createHonoClient(
        typeof window !== 'undefined'
          ? window.location.origin
          : 'http://localhost:3001',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )
      const res = await client.admin['featured-products'].$get()
      return res.json()
    },

    createFeaturedProduct: async (token: string, data: {
      productId: string
      title: string
      imageUrl?: string
      affiliateLink: string
      categoryId: number
      priority?: number
    }) => {
      const client = createHonoClient(
        typeof window !== 'undefined'
          ? window.location.origin
          : 'http://localhost:3001',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )
      const res = await client.admin['featured-products'].$post({
        json: data
      })
      return res.json()
    },

    getCategories: async (token: string) => {
      const client = createHonoClient(
        typeof window !== 'undefined'
          ? window.location.origin
          : 'http://localhost:3001',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )
      const res = await client.admin.categories.$get()
      return res.json()
    },

    getStatus: async (token: string) => {
      const client = createHonoClient(
        typeof window !== 'undefined'
          ? window.location.origin
          : 'http://localhost:3001',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )
      const res = await client.admin.status.$get()
      return res.json()
    },
  },

  // Additional API endpoints will be added when implementing features
}

// Export types for use in components
export type HonoClient = ReturnType<typeof createHonoClient>
export type { AppType }
