{"name": "coupon-finder", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start", "preview": "vinxi preview", "build:cf": "vinxi build --preset cloudflare-module", "deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --env development", "deploy:prod": "wrangler deploy --env production", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "prepare": "husky", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop", "create-admin": "tsx src/scripts/create-admin.ts"}, "dependencies": {"@hono/node-server": "^1.14.3", "@hono/zod-validator": "^0.7.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-form": "^1.12.0", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-router": "^1.120.13", "@tanstack/react-router-devtools": "^1.120.13", "@tanstack/react-start": "^1.120.13", "@tanstack/react-table": "^8.21.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.1", "hono": "^4.7.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "vinxi": "0.5.3", "zod": "^3.25.0", "zustand": "^5.0.5"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250531.0", "@eslint/js": "^9.28.0", "@libsql/client": "^0.15.8", "@tailwindcss/postcss": "^4.1.8", "@tanstack/router-cli": "^1.120.13", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.31.1", "postcss": "^8.5.1", "tailwindcss": "4.1.8", "tsx": "^4.19.4", "typescript": "^5.7.2", "typescript-eslint": "^8.33.0", "vite-tsconfig-paths": "^5.1.4", "wrangler": "4"}}