import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { cache } from 'hono/cache'
import type { Bindings, Variables } from '../hono-app'
import { createAccessTradeClient, AccessTradeError } from '../../lib/accesstrade-client'

// Validation schemas
const getCampaignsSchema = z.object({
  category: z.string().optional(),
  merchant: z.string().optional(),
  status: z.enum(['active', 'upcoming', 'expired']).default('active'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('20'),
  offset: z.string().transform(Number).pipe(z.number().min(0)).default('0'),
})

const getCampaignSchema = z.object({
  id: z.string().min(1, 'Campaign ID is required'),
})

// Create campaigns routes
export const campaignsRoutes = new Hono<{
  Bindings: Bindings
  Variables: Variables
}>()

// Cache middleware for campaign data
campaignsRoutes.use('*', cache({
  cacheName: 'campaigns',
  cacheControl: 'max-age=1800', // 30 minutes
}))

// Get campaigns endpoint
campaignsRoutes.get('/',
  zValidator('query', getCampaignsSchema),
  async (c) => {
    try {
      const params = c.req.valid('query')
      
      // Generate cache key
      const cacheKey = `campaigns:list:${JSON.stringify(params)}`
      
      // Try to get from cache first (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Campaigns retrieved from cache',
            data,
            cached: true,
          })
        }
      }
      
      // AccessTrade API call - Get campaigns with approval=successful filter
      const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')

      const campaignsResponse = await accessTradeClient.getCampaigns({
        approval: 'successful',
        limit: params.limit,
        page: Math.floor(params.offset / params.limit) + 1
      })

      // Filter campaigns based on parameters
      let filteredCampaigns = campaignsResponse.data

      // Filter by merchant
      if (params.merchant) {
        filteredCampaigns = filteredCampaigns.filter(campaign =>
          campaign.merchant.toLowerCase().includes(params.merchant!.toLowerCase())
        )
      }

      // Filter by category
      if (params.category) {
        filteredCampaigns = filteredCampaigns.filter(campaign =>
          campaign.category.toLowerCase().includes(params.category!.toLowerCase())
        )
      }

      // Filter by status
      if (params.status) {
        const now = new Date()
        filteredCampaigns = filteredCampaigns.filter(campaign => {
          const startDate = new Date(campaign.start_time)
          const endDate = campaign.end_time ? new Date(campaign.end_time) : null

          switch (params.status) {
            case 'active':
              return campaign.status === 1 && startDate <= now && (!endDate || endDate >= now)
            case 'upcoming':
              return campaign.status === 1 && startDate > now
            case 'expired':
              return campaign.status !== 1 || (endDate && endDate < now)
            default:
              return true
          }
        })
      }

      // Apply pagination
      const paginatedCampaigns = filteredCampaigns.slice(params.offset, params.offset + params.limit)

      // Transform to our format
      const campaigns = {
        campaigns: paginatedCampaigns.map(campaign => ({
          id: campaign.id,
          name: campaign.name,
          description: campaign.description,
          merchant: campaign.merchant,
          category: campaign.category,
          subCategory: campaign.sub_category,
          status: mapCampaignStatus(campaign.status, campaign.start_time, campaign.end_time),
          approval: campaign.approval,
          startDate: campaign.start_time,
          endDate: campaign.end_time,
          url: campaign.url,
          cookieDuration: campaign.cookie_duration,
          conversionPolicy: campaign.conversion_policy,
          cookiePolicy: campaign.cookie_policy,
          scope: campaign.scope,
          type: campaign.type,
          affiliateLink: campaign.url, // Use campaign URL as affiliate link
        })),
        total: filteredCampaigns.length,
        limit: params.limit,
        offset: params.offset,
      }
      
      // Cache the results (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(campaigns),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 3600 }
        )
      }
      
      return c.json({
        success: true,
        message: 'Campaigns retrieved successfully',
        data: campaigns,
        cached: false,
      })
      
    } catch (error) {
      console.error('Get campaigns error:', error)
      return c.json({
        error: 'Failed to get campaigns',
        message: 'An error occurred while fetching campaigns'
      }, 500)
    }
  }
)

// Get campaign by ID
campaignsRoutes.get('/:id',
  zValidator('param', getCampaignSchema),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      
      // Generate cache key
      const cacheKey = `campaign:${id}`
      
      // Try to get from cache first (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Campaign retrieved from cache',
            data,
            cached: true,
          })
        }
      }

      // AccessTrade API call - Get specific campaign by ID (only approved campaigns)
      const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')

      const campaignsResponse = await accessTradeClient.getCampaigns({
        approval: 'successful',
        campaign_id: id
      })
      const accessTradeCampaign = campaignsResponse.data.find(c => c.id === id)

      if (!accessTradeCampaign) {
        return c.json({
          error: 'Campaign not found',
          message: 'The requested campaign was not found'
        }, 404)
      }

      // Transform to our format
      const campaign = {
        id: accessTradeCampaign.id,
        name: accessTradeCampaign.name,
        description: accessTradeCampaign.description,
        merchant: accessTradeCampaign.merchant,
        category: accessTradeCampaign.category,
        subCategory: accessTradeCampaign.sub_category,
        status: mapCampaignStatus(accessTradeCampaign.status, accessTradeCampaign.start_time, accessTradeCampaign.end_time),
        approval: accessTradeCampaign.approval,
        startDate: accessTradeCampaign.start_time,
        endDate: accessTradeCampaign.end_time,
        url: accessTradeCampaign.url,
        cookieDuration: accessTradeCampaign.cookie_duration,
        conversionPolicy: accessTradeCampaign.conversion_policy,
        cookiePolicy: accessTradeCampaign.cookie_policy,
        scope: accessTradeCampaign.scope,
        type: accessTradeCampaign.type,
        affiliateLink: accessTradeCampaign.url,
      }
      

      
      // Cache the result (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(campaign),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 3600 }
        )
      }
      
      return c.json({
        success: true,
        message: 'Campaign retrieved successfully',
        data: campaign,
        cached: false,
      })
      
    } catch (error) {
      console.error('Get campaign error:', error)
      return c.json({
        error: 'Failed to get campaign',
        message: 'An error occurred while fetching campaign details'
      }, 500)
    }
  }
)

// Helper functions for AccessTrade integration
function mapCampaignStatus(status: number, startTime: string, endTime: string | null): 'active' | 'upcoming' | 'expired' {
  const now = new Date()
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : null

  if (status !== 1) {
    return 'expired'
  }

  if (start > now) {
    return 'upcoming'
  }

  if (end && end < now) {
    return 'expired'
  }

  return 'active'
}

export default campaignsRoutes
