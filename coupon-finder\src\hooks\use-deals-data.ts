import { useQuery } from '@tanstack/react-query'
import { api } from '@/api/hono-client'

// Helper function để tính discount percentage đúng cách
function calculateDiscountPercentage(price: number, originalPrice: number): number {
  // Validate input
  if (!price || !originalPrice || originalPrice <= 0 || price < 0) {
    return Math.floor(Math.random() * 25) + 5 // Fallback: random 5-30%
  }

  // Nếu price >= originalPrice thì không có discount
  if (price >= originalPrice) {
    return 0
  }

  // Tính discount percentage
  const discount = ((originalPrice - price) / originalPrice) * 100

  // Validate kết quả: discount phải trong khoảng 0-100%
  if (discount < 0 || discount > 100) {
    return Math.floor(Math.random() * 25) + 5 // Fallback: random 5-30%
  }

  // Làm tròn về số nguyên
  return Math.round(discount)
}

// Types cho deals data
export interface TopSellingProduct {
  id: string
  name: string
  store: string
  price: number
  rating: number
  monthlySales: number
  image: string
  merchant: string
  affiliateLink: string
}

export interface BestDeal {
  id: string
  name: string
  store: string
  price: number
  originalPrice: number
  discount: number
  rating: number
  reviews: number
  image: string
  isLimitedTime: boolean
  expiryTime?: string
  merchant: string
  affiliateLink: string
}



// Hook để lấy top selling products từ campaigns
export function useTopSellingProducts() {
  return useQuery({
    queryKey: ['deals', 'top-selling'],
    queryFn: async (): Promise<TopSellingProduct[]> => {
      try {
        // Sử dụng campaigns API để lấy các chiến dịch active
        const data = await api.campaigns.getAll({
          status: 'active',
          limit: 6,
          offset: 0
        })

        console.log('Top selling campaigns response:', data) // Debug log

        if (!data.success || !data.data) {
          throw new Error('Invalid response format')
        }

        // Check if campaigns array exists
        if (!data.data.campaigns || !Array.isArray(data.data.campaigns)) {
          console.warn('No campaigns array found in top selling response:', data.data)
          return []
        }

        return data.data.campaigns.map((campaign: any, index: number) => ({
          id: campaign.id || `top-${index}`,
          name: campaign.name || 'Chiến dịch không tên',
          store: campaign.merchant || 'Store',
          price: Math.floor(Math.random() * 1000000) + 100000,
          rating: 4.5 + Math.random() * 0.5, // Random rating 4.5-5.0
          monthlySales: Math.floor(Math.random() * 10000) + 1000,
          image: campaign.logo || '/api/placeholder/300/300',
          merchant: campaign.merchant || 'Unknown',
          affiliateLink: campaign.url || '#'
        }))
      } catch (error) {
        console.error('Error fetching top selling campaigns:', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

// Hook để lấy best deals từ top selling products API
export function useBestDeals() {
  return useQuery({
    queryKey: ['deals', 'best-deals'],
    queryFn: async (): Promise<BestDeal[]> => {
      try {
        // Sử dụng top selling products API với fallback về products API
        let data = await api.products.getTopSelling(undefined, 12, 0)

        // Nếu top selling không có data, fallback về products API
        if (!data.success || !data.data || !data.data.products || data.data.products.length === 0) {
          data = await api.products.getAll({
            limit: 12,
            offset: 0
          })
        }

        if (!data.success || !data.data) {
          throw new Error('Invalid response format')
        }

        // Check if products array exists
        if (!data.data.products || !Array.isArray(data.data.products)) {
          return []
        }

        return data.data.products.map((product: any, index: number) => {
          const price = Number(product.price) || 0
          const originalPrice = Number(product.originalPrice) || (price ? price * 1.15 : 0)

          return {
            id: product.id || `deal-${index}`,
            name: product.name || product.description || 'Sản phẩm không tên',
            store: product.merchant || 'Store',
            price,
            originalPrice,
            discount: calculateDiscountPercentage(price, originalPrice),
            rating: Number(product.rating) || 4.5,
            reviews: Number(product.reviewCount) || Math.floor(Math.random() * 1000) + 100,
            image: product.imageUrl || product.image || '/api/placeholder/300/300',
            isLimitedTime: Math.random() > 0.7, // 30% chance
            expiryTime: Math.random() > 0.5 ? `${Math.floor(Math.random() * 24)} giờ` : `${Math.floor(Math.random() * 7)} ngày`,
            merchant: product.merchant || 'Unknown',
            affiliateLink: product.affiliateLink || product.aff_link || '#'
          }
        })
      } catch (error) {
        console.error('Error fetching best deals (top selling):', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

// Hook tổng hợp để lấy tất cả deals data
export function useDealsData() {
  const topSelling = useTopSellingProducts()
  const bestDeals = useBestDeals()

  return {
    topSelling,
    bestDeals,
    isLoading: topSelling.isLoading || bestDeals.isLoading,
    isError: topSelling.isError || bestDeals.isError,
    error: topSelling.error || bestDeals.error
  }
}
