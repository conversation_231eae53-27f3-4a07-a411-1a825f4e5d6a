import * as React from 'react'
import { useQuery } from '@tanstack/react-query'
import { X, Copy, ExternalLink, Check, Calendar, Store, Tag, TrendingUp } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from './ui/dialog'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Card, CardContent } from './ui/card'
import { Separator } from './ui/separator'
import { api } from '@/api/hono-client'
import { cn } from '@/lib/utils'

interface CouponDetailModalProps {
  couponId: string | null
  isOpen: boolean
  onClose: () => void
}

export function CouponDetailModal({
  couponId,
  isOpen,
  onClose,
}: CouponDetailModalProps) {
  const [copied, setCopied] = React.useState(false)
  const [isTracking, setIsTracking] = React.useState(false)

  // Fetch coupon details với real-time data
  const {
    data: coupon,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['coupon', 'detail', couponId],
    queryFn: async () => {
      if (!couponId) return null
      
      const result = await api.coupons.getById(couponId)
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch coupon details')
      }

      return result.data
    },
    enabled: !!couponId && isOpen,
    staleTime: 30 * 1000, // 30 seconds for real-time feel
    refetchInterval: 60 * 1000, // Refetch every minute when modal is open
    refetchIntervalInBackground: false,
  })

  const handleCopyCode = async () => {
    if (!coupon?.code) return

    try {
      await navigator.clipboard.writeText(coupon.code)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      
      // Track copy action
      if (couponId) {
        await trackAction('copy')
      }
    } catch (err) {
      console.error('Failed to copy code:', err)
    }
  }

  const handleAffiliateClick = async () => {
    if (!coupon?.affiliateLink || !coupon?.aff_link) return
    
    // Track click action
    if (couponId) {
      await trackAction('click')
    }
    
    // Open affiliate link
    const link = coupon.affiliateLink || coupon.aff_link
    window.open(link, '_blank', 'noopener,noreferrer')
  }

  const trackAction = async (action: 'copy' | 'click' | 'view') => {
    if (!couponId || isTracking) return
    
    setIsTracking(true)
    try {
      await api.coupons.track(couponId, { action })
    } catch (error) {
      console.error('Failed to track action:', error)
    } finally {
      setIsTracking(false)
    }
  }

  // Track view when modal opens
  React.useEffect(() => {
    if (isOpen && couponId) {
      trackAction('view')
    }
  }, [isOpen, couponId])

  const isExpired = coupon?.end_time && new Date(coupon.end_time) < new Date()
  const daysUntilExpiry = coupon?.end_time 
    ? Math.ceil((new Date(coupon.end_time).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    : null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold pr-8">
            Chi tiết mã giảm giá
          </DialogTitle>
          <DialogDescription>
            Thông tin chi tiết và cách sử dụng mã giảm giá
          </DialogDescription>
        </DialogHeader>

        {/* Loading State */}
        {isLoading && (
          <div className="space-y-4 animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-12 bg-gray-200 rounded"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card className="text-center py-8">
            <CardContent>
              <div className="text-red-500 text-lg mb-2">⚠️ Có lỗi xảy ra</div>
              <p className="text-gray-600 mb-4">
                {error instanceof Error ? error.message : 'Không thể tải thông tin mã giảm giá'}
              </p>
              <Button onClick={() => refetch()}>
                Thử lại
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Coupon Details */}
        {coupon && (
          <div className="space-y-6">
            {/* Header với discount badge */}
            <div className="relative">
              {coupon.image && (
                <div className="w-full h-48 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg overflow-hidden">
                  <img 
                    src={coupon.image} 
                    alt={coupon.title || coupon.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                </div>
              )}
              
              <div className="absolute top-4 right-4">
                <Badge variant="success" className="text-lg font-bold px-3 py-1">
                  {coupon.discount || 'Ưu đãi đặc biệt'}
                </Badge>
              </div>
            </div>

            {/* Title và description */}
            <div>
              <h2 className="text-2xl font-bold mb-2">
                {coupon.title || coupon.name}
              </h2>
              <p className="text-gray-600 leading-relaxed">
                {coupon.description || coupon.content}
              </p>
            </div>

            {/* Coupon Code Section */}
            <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-dashed border-blue-200">
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <div className="text-sm font-medium text-gray-600">
                    MÃ GIẢM GIÁ
                  </div>
                  <div className="text-3xl font-mono font-bold text-blue-600 tracking-wider">
                    {coupon.code}
                  </div>
                  <div className="flex gap-3 justify-center">
                    <Button
                      onClick={handleCopyCode}
                      disabled={isExpired}
                      className="flex-1 max-w-xs"
                    >
                      {copied ? (
                        <>
                          <Check className="h-4 w-4 mr-2" />
                          Đã sao chép!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" />
                          Sao chép mã
                        </>
                      )}
                    </Button>
                    {(coupon.affiliateLink || coupon.aff_link) && (
                      <Button
                        variant="gradient"
                        onClick={handleAffiliateClick}
                        disabled={isExpired || isTracking}
                        className="flex-1 max-w-xs"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Dùng ngay
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Separator />

            {/* Thông tin chi tiết */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Store className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Cửa hàng:</span>
                  <Badge variant="outline">
                    {coupon.merchant || coupon.store}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Danh mục:</span>
                  <Badge variant="outline">
                    {coupon.category || coupon.categories?.[0]?.category_name_show || 'Tổng hợp'}
                  </Badge>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Hạn sử dụng:</span>
                  <Badge variant={isExpired ? 'destructive' : daysUntilExpiry && daysUntilExpiry <= 3 ? 'warning' : 'success'}>
                    {isExpired 
                      ? 'Đã hết hạn' 
                      : coupon.end_time 
                        ? new Date(coupon.end_time).toLocaleDateString('vi-VN')
                        : 'Không giới hạn'
                    }
                  </Badge>
                </div>

                {daysUntilExpiry !== null && daysUntilExpiry > 0 && (
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Còn lại:</span>
                    <Badge variant={daysUntilExpiry <= 3 ? 'warning' : 'outline'}>
                      {daysUntilExpiry} ngày
                    </Badge>
                  </div>
                )}
              </div>
            </div>

            {/* Banners nếu có */}
            {coupon.banners && coupon.banners.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3">Hình ảnh khuyến mãi</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {coupon.banners.slice(0, 4).map((banner: any, index: number) => (
                    <div key={index} className="rounded-lg overflow-hidden border">
                      <img 
                        src={banner.link} 
                        alt={`Banner ${index + 1}`}
                        className="w-full h-32 object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                        }}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Cảnh báo hết hạn */}
            {isExpired && (
              <Card className="bg-red-50 border-red-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 text-red-600">
                    <Calendar className="h-4 w-4" />
                    <span className="font-medium">Mã giảm giá này đã hết hạn sử dụng</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Hướng dẫn sử dụng */}
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-2">💡 Hướng dẫn sử dụng</h3>
                <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                  <li>Sao chép mã giảm giá bằng cách nhấn nút "Sao chép mã"</li>
                  <li>Nhấn "Dùng ngay" để đến trang web của cửa hàng</li>
                  <li>Thêm sản phẩm vào giỏ hàng và tiến hành thanh toán</li>
                  <li>Dán mã giảm giá vào ô "Mã khuyến mãi" khi thanh toán</li>
                  <li>Kiểm tra giảm giá đã được áp dụng và hoàn tất đơn hàng</li>
                </ol>
              </CardContent>
            </Card>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
