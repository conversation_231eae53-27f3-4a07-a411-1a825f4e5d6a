import { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { ZodError } from 'zod'

/**
 * Custom error classes for better error handling
 */
export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public code?: string
  ) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class DatabaseError extends Error {
  constructor(
    message: string,
    public operation?: string,
    public table?: string
  ) {
    super(message)
    this.name = 'DatabaseError'
  }
}

export class AccessTradeError extends Error {
  constructor(
    message: string,
    public endpoint?: string,
    public statusCode?: number
  ) {
    super(message)
    this.name = 'AccessTradeError'
  }
}

export class AuthenticationError extends Error {
  constructor(message: string = 'Authentication required') {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends Error {
  constructor(message: string = 'Insufficient permissions') {
    super(message)
    this.name = 'AuthorizationError'
  }
}

export class RateLimitError extends Error {
  constructor(
    message: string = 'Rate limit exceeded',
    public retryAfter?: number
  ) {
    super(message)
    this.name = 'RateLimitError'
  }
}

/**
 * Error response interface
 */
export interface ErrorResponse {
  error: string
  message: string
  code?: string
  field?: string
  details?: any
  requestId?: string
  timestamp: string
  path?: string
  method?: string
  stack?: string
}

/**
 * Format Zod validation errors
 */
export function formatZodError(error: ZodError): ErrorResponse {
  const firstIssue = error.issues[0]
  return {
    error: 'Validation Error',
    message: firstIssue?.message || 'Invalid input data',
    code: firstIssue?.code,
    field: firstIssue?.path.join('.') || undefined,
    details: error.issues.map(issue => ({
      field: issue.path.join('.'),
      message: issue.message,
      code: issue.code,
    })),
    timestamp: new Date().toISOString(),
  }
}

/**
 * Global error handler middleware
 */
export function errorHandler() {
  return async (err: Error, c: Context) => {
    const isDev = c.env?.NODE_ENV === 'development'
    const requestId = c.get('requestId')
    
    console.error('Error occurred:', {
      name: err.name,
      message: err.message,
      stack: err.stack,
      requestId,
      path: c.req.path,
      method: c.req.method,
    })

    // Handle HTTPException (thrown by Hono middleware)
    if (err instanceof HTTPException) {
      return err.getResponse()
    }

    // Handle Zod validation errors
    if (err instanceof ZodError) {
      const errorResponse = formatZodError(err)
      return c.json({
        ...errorResponse,
        requestId,
        path: c.req.path,
        method: c.req.method,
      }, 400)
    }

    // Handle custom validation errors
    if (err instanceof ValidationError) {
      return c.json({
        error: 'Validation Error',
        message: err.message,
        field: err.field,
        code: err.code,
        requestId,
        timestamp: new Date().toISOString(),
        path: c.req.path,
        method: c.req.method,
      }, 400)
    }

    // Handle authentication errors
    if (err instanceof AuthenticationError) {
      return c.json({
        error: 'Authentication Error',
        message: err.message,
        requestId,
        timestamp: new Date().toISOString(),
        path: c.req.path,
        method: c.req.method,
      }, 401)
    }

    // Handle authorization errors
    if (err instanceof AuthorizationError) {
      return c.json({
        error: 'Authorization Error',
        message: err.message,
        requestId,
        timestamp: new Date().toISOString(),
        path: c.req.path,
        method: c.req.method,
      }, 403)
    }

    // Handle rate limit errors
    if (err instanceof RateLimitError) {
      const response = c.json({
        error: 'Rate Limit Exceeded',
        message: err.message,
        requestId,
        timestamp: new Date().toISOString(),
        path: c.req.path,
        method: c.req.method,
      }, 429)
      
      if (err.retryAfter) {
        response.headers.set('Retry-After', err.retryAfter.toString())
      }
      
      return response
    }

    // Handle database errors
    if (err instanceof DatabaseError) {
      return c.json({
        error: 'Database Error',
        message: isDev ? err.message : 'Database operation failed',
        operation: isDev ? err.operation : undefined,
        table: isDev ? err.table : undefined,
        requestId,
        timestamp: new Date().toISOString(),
        path: c.req.path,
        method: c.req.method,
      }, 500)
    }

    // Handle AccessTrade API errors
    if (err instanceof AccessTradeError) {
      return c.json({
        error: 'External API Error',
        message: isDev ? err.message : 'External service unavailable',
        endpoint: isDev ? err.endpoint : undefined,
        requestId,
        timestamp: new Date().toISOString(),
        path: c.req.path,
        method: c.req.method,
      }, err.statusCode || 502)
    }

    // Handle generic errors
    return c.json({
      error: 'Internal Server Error',
      message: isDev ? err.message : 'Something went wrong',
      requestId,
      timestamp: new Date().toISOString(),
      path: c.req.path,
      method: c.req.method,
      ...(isDev && { stack: err.stack }),
    }, 500)
  }
}

/**
 * Not found handler
 */
export function notFoundHandler() {
  return (c: Context) => {
    return c.json({
      error: 'Not Found',
      message: 'The requested resource was not found',
      path: c.req.path,
      method: c.req.method,
      requestId: c.get('requestId'),
      timestamp: new Date().toISOString(),
    }, 404)
  }
}

/**
 * Async error wrapper for route handlers
 */
export function asyncHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return (...args: T): Promise<R> => {
    return Promise.resolve(fn(...args)).catch((error) => {
      throw error
    })
  }
}
