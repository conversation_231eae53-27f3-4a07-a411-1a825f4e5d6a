/**
 * URL Parser Utilities for Shopee Links
 * Implements URL parsing logic cho Shopee links với Hono validation
 */

export interface ShopeeUrlInfo {
  isValid: boolean
  originalUrl: string
  productId?: string
  shopId?: string
  productName?: string
  categoryId?: string
  merchant: 'shopee'
  error?: string
}

/**
 * Parse Shopee URL để lấy thông tin sản phẩm
 * Supports various Shopee URL formats:
 * - https://shopee.vn/product-name-i.123.456
 * - https://shopee.vn/shop/123/456
 * - https://shopee.vn/product-name-i.123.456?sp_atk=...
 */
export function parseShopeeUrl(url: string): ShopeeUrlInfo {
  const result: ShopeeUrlInfo = {
    isValid: false,
    originalUrl: url,
    merchant: 'shopee'
  }

  try {
    // Basic URL validation
    if (!url || typeof url !== 'string') {
      result.error = 'URL không hợp lệ'
      return result
    }

    // Clean and normalize URL
    const cleanUrl = url.trim()
    
    // Check if it's a Shopee URL
    const shopeeRegex = /^https?:\/\/(www\.)?(shopee\.vn|shopee\.com\.vn)/i
    if (!shopeeRegex.test(cleanUrl)) {
      result.error = 'Không phải URL Shopee hợp lệ'
      return result
    }

    const urlObj = new URL(cleanUrl)
    const pathname = urlObj.pathname

    // Parse different Shopee URL patterns
    
    // Pattern 1: Product URL - https://shopee.vn/product-name-i.shopId.productId
    const productPattern = /^\/(.+)-i\.(\d+)\.(\d+)$/
    const productMatch = pathname.match(productPattern)
    
    if (productMatch) {
      const [, productSlug, shopId, productId] = productMatch
      
      result.isValid = true
      result.productId = productId
      result.shopId = shopId
      result.productName = decodeURIComponent(productSlug.replace(/-/g, ' '))
      
      return result
    }

    // Pattern 2: Shop URL - https://shopee.vn/shop/shopId/productId
    const shopPattern = /^\/shop\/(\d+)\/(\d+)$/
    const shopMatch = pathname.match(shopPattern)
    
    if (shopMatch) {
      const [, shopId, productId] = shopMatch
      
      result.isValid = true
      result.productId = productId
      result.shopId = shopId
      
      return result
    }

    // Pattern 3: Category URL - https://shopee.vn/category-name-cat.categoryId
    const categoryPattern = /^\/(.+)-cat\.(\d+)$/
    const categoryMatch = pathname.match(categoryPattern)
    
    if (categoryMatch) {
      const [, categorySlug, categoryId] = categoryMatch
      
      result.isValid = true
      result.categoryId = categoryId
      result.productName = decodeURIComponent(categorySlug.replace(/-/g, ' '))
      
      return result
    }

    // Pattern 4: Search URL - https://shopee.vn/search?keyword=...
    if (pathname === '/search') {
      const keyword = urlObj.searchParams.get('keyword')
      if (keyword) {
        result.isValid = true
        result.productName = decodeURIComponent(keyword)
        return result
      }
    }

    // If no pattern matches
    result.error = 'Định dạng URL Shopee không được hỗ trợ'
    return result

  } catch (error) {
    result.error = 'Lỗi khi phân tích URL'
    return result
  }
}

/**
 * Validate if URL is a valid Shopee product URL
 */
export function isValidShopeeUrl(url: string): boolean {
  const info = parseShopeeUrl(url)
  return info.isValid
}

/**
 * Extract product name from Shopee URL for search purposes
 */
export function extractProductNameFromUrl(url: string): string | null {
  const info = parseShopeeUrl(url)
  return info.isValid ? info.productName || null : null
}

/**
 * Generate search keywords from Shopee URL
 */
export function generateSearchKeywords(url: string): string[] {
  const info = parseShopeeUrl(url)
  
  if (!info.isValid || !info.productName) {
    return []
  }

  // Split product name into keywords and clean them
  const keywords = info.productName
    .toLowerCase()
    .split(/[\s\-_,\.]+/)
    .filter(keyword => keyword.length > 2) // Remove short words
    .filter(keyword => !/^\d+$/.test(keyword)) // Remove pure numbers
    .slice(0, 5) // Limit to 5 keywords

  return keywords
}

/**
 * Create affiliate tracking URL (placeholder for future implementation)
 */
export function createAffiliateUrl(originalUrl: string, affiliateId?: string): string {
  // For now, return original URL
  // In the future, this will add AccessTrade affiliate parameters
  return originalUrl
}

/**
 * URL validation utilities
 */
export const urlValidation = {
  /**
   * Check if string is a valid URL
   */
  isValidUrl: (str: string): boolean => {
    try {
      new URL(str)
      return true
    } catch {
      return false
    }
  },

  /**
   * Check if URL is from Shopee domain
   */
  isShopeeUrl: (url: string): boolean => {
    try {
      const urlObj = new URL(url)
      return /^(www\.)?(shopee\.vn|shopee\.com\.vn)$/i.test(urlObj.hostname)
    } catch {
      return false
    }
  },

  /**
   * Normalize URL by removing unnecessary parameters
   */
  normalizeUrl: (url: string): string => {
    try {
      const urlObj = new URL(url)
      
      // Remove tracking parameters
      const trackingParams = ['sp_atk', 'utm_source', 'utm_medium', 'utm_campaign', 'fbclid']
      trackingParams.forEach(param => {
        urlObj.searchParams.delete(param)
      })
      
      return urlObj.toString()
    } catch {
      return url
    }
  }
}

/**
 * Example usage and test cases
 */
export const examples = {
  validUrls: [
    'https://shopee.vn/Áo-thun-nam-cotton-100-i.123.456789',
    'https://shopee.vn/shop/123/456789',
    'https://shopee.vn/thoi-trang-nam-cat.11035567',
    'https://shopee.vn/search?keyword=áo%20thun%20nam'
  ],
  
  invalidUrls: [
    'https://lazada.vn/product',
    'https://tiki.vn/product',
    'not-a-url',
    'https://shopee.vn'
  ]
}
