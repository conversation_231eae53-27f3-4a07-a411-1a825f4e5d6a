import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { parseShopeeUrl, urlValidation, examples } from '@/lib/url-parser'
import { api } from '@/api/hono-client'

export const Route = createFileRoute('/search-demo')({
  component: SearchDemo,
})

function SearchDemo() {
  const [testUrl, setTestUrl] = useState('')
  const [urlInfo, setUrlInfo] = useState<any>(null)
  const [searchResults, setSearchResults] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleParseUrl = () => {
    const info = parseShopeeUrl(testUrl)
    setUrlInfo(info)
  }

  const handleTestSearch = async () => {
    if (!testUrl) return
    
    setIsLoading(true)
    try {
      const info = parseShopeeUrl(testUrl)
      
      let searchParams: any = {
        limit: 10,
        offset: 0,
      }

      if (info.isValid) {
        searchParams.url = testUrl
        if (info.productName) {
          searchParams.keyword = info.productName
        }
      } else {
        searchParams.keyword = testUrl
      }

      const result = await api.coupons.search(searchParams)
      setSearchResults(result)
    } catch (error) {
      setSearchResults({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Unknown error' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleExampleUrl = (url: string) => {
    setTestUrl(url)
    setUrlInfo(null)
    setSearchResults(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">🧪 Search Functionality Demo</CardTitle>
            <p className="text-gray-600">
              Test search components, URL parsing, và Hono API integration
            </p>
          </CardHeader>
        </Card>

        {/* URL Parser Test */}
        <Card>
          <CardHeader>
            <CardTitle>URL Parser Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                placeholder="Nhập URL Shopee hoặc từ khóa để test..."
                value={testUrl}
                onChange={(e) => setTestUrl(e.target.value)}
                className="flex-1"
              />
              <Button onClick={handleParseUrl}>Parse URL</Button>
              <Button onClick={handleTestSearch} disabled={isLoading}>
                {isLoading ? 'Testing...' : 'Test Search'}
              </Button>
            </div>

            {/* Example URLs */}
            <div>
              <p className="text-sm font-medium mb-2">Example URLs:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {examples.validUrls.map((url, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleExampleUrl(url)}
                    className="text-left justify-start text-xs"
                  >
                    {url.length > 50 ? url.substring(0, 50) + '...' : url}
                  </Button>
                ))}
              </div>
            </div>

            {/* URL Info Display */}
            {urlInfo && (
              <Card className="bg-gray-50">
                <CardHeader>
                  <CardTitle className="text-lg">URL Parse Results</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Valid:</span>{' '}
                      <Badge variant={urlInfo.isValid ? 'success' : 'destructive'}>
                        {urlInfo.isValid ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    
                    {urlInfo.productName && (
                      <div>
                        <span className="font-medium">Product Name:</span>{' '}
                        {urlInfo.productName}
                      </div>
                    )}
                    
                    {urlInfo.productId && (
                      <div>
                        <span className="font-medium">Product ID:</span>{' '}
                        {urlInfo.productId}
                      </div>
                    )}
                    
                    {urlInfo.shopId && (
                      <div>
                        <span className="font-medium">Shop ID:</span>{' '}
                        {urlInfo.shopId}
                      </div>
                    )}
                    
                    {urlInfo.categoryId && (
                      <div>
                        <span className="font-medium">Category ID:</span>{' '}
                        {urlInfo.categoryId}
                      </div>
                    )}
                    
                    <div>
                      <span className="font-medium">Merchant:</span>{' '}
                      {urlInfo.merchant}
                    </div>
                    
                    {urlInfo.error && (
                      <div className="col-span-2">
                        <span className="font-medium text-red-600">Error:</span>{' '}
                        {urlInfo.error}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResults && (
          <Card>
            <CardHeader>
              <CardTitle>Search Results</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto">
                {JSON.stringify(searchResults, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* URL Validation Tests */}
        <Card>
          <CardHeader>
            <CardTitle>URL Validation Tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Valid Shopee URLs:</h4>
                <div className="space-y-1">
                  {examples.validUrls.map((url, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <Badge variant="success">✓</Badge>
                      <span className="font-mono text-xs">{url}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Invalid URLs:</h4>
                <div className="space-y-1">
                  {examples.invalidUrls.map((url, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <Badge variant="destructive">✗</Badge>
                      <span className="font-mono text-xs">{url}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Status */}
        <Card>
          <CardHeader>
            <CardTitle>API Integration Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <Badge variant="success" className="mb-2">✅ Connected</Badge>
                <p className="text-sm">Hono RPC Client</p>
              </div>
              <div className="text-center">
                <Badge variant="success" className="mb-2">✅ Ready</Badge>
                <p className="text-sm">URL Parser</p>
              </div>
              <div className="text-center">
                <Badge variant="success" className="mb-2">✅ Active</Badge>
                <p className="text-sm">Search Form</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex gap-4 justify-center">
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/'}
              >
                ← Back to Home
              </Button>
              <Button 
                onClick={() => window.location.href = '/search-results?q=áo+thun&type=keyword&sortBy=discount'}
              >
                View Search Results →
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
