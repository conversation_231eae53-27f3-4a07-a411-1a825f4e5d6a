import * as React from 'react';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CouponCard } from '@/components/coupon-card';
import { CouponSearchForm } from '@/components/forms/coupon-search-form';
import { CouponList } from '@/components/coupon-list';
import { CouponDetailModal } from '@/components/coupon-detail-modal';
import type { CouponSearchFormData } from '@/lib/validation-schemas';
import type { FormSubmissionResult } from '@/lib/form-utils';
import { api } from '@/api/hono-client';
import { parseShopeeUrl } from '@/lib/url-parser';

export const Route = createFileRoute('/')({
  component: Home,
});

function Home() {
  const navigate = useNavigate();
  const [selectedCouponId, setSelectedCouponId] = React.useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  // Handle coupon search submission với Hono API thực sự
  const handleCouponSearch = async (data: CouponSearchFormData): Promise<FormSubmissionResult<CouponSearchFormData>> => {
    try {
      console.log('🔍 Form submitted with data:', data);
      console.log('🔍 Starting search process...');

      let searchParams: any = {
        category: data.category,
        limit: 20,
        offset: 0,
      };

      // Xử lý search type
      if (data.searchType === 'url') {
        // Parse Shopee URL để lấy thông tin sản phẩm
        const urlInfo = parseShopeeUrl(data.searchValue);
        if (!urlInfo.isValid) {
          return {
            success: false,
            message: 'URL Shopee không hợp lệ. Vui lòng kiểm tra lại URL.'
          };
        }

        searchParams.url = data.searchValue;
        if (urlInfo.productName) {
          searchParams.keyword = urlInfo.productName;
        }
      } else {
        // Tìm kiếm theo keyword
        searchParams.keyword = data.searchValue;
      }

      // Gọi Hono API để tìm kiếm coupons từ AccessTrade với timeout
      console.log('🔍 Calling AccessTrade API via Hono...', searchParams);

      // Add timeout to API call
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('API call timeout after 10 seconds')), 10000);
      });

      const apiPromise = api.coupons.search(searchParams);

      const result = await Promise.race([apiPromise, timeoutPromise]);
      console.log('🔍 API result:', result);

      if (!result.success) {
        console.error('🔍 API call failed:', result.message);
        console.log('🔍 API failed, but still navigating to search results...');

        // Navigate anyway even if API fails
        const searchQuery = {
          q: data.searchValue,
          type: data.searchType,
          category: data.category || '',
          sortBy: data.sortBy,
        };

        navigate({
          to: '/search-results',
          search: searchQuery
        });

        return {
          success: true, // Return success to prevent form error state
          data,
          message: 'Đang tìm kiếm mã giảm giá...'
        };
      }

      // Navigate to search results page với query parameters
      console.log('🔍 API call successful, navigating to search results...');
      const searchQuery = {
        q: data.searchValue,
        type: data.searchType,
        category: data.category || '',
        sortBy: data.sortBy,
      };
      console.log('🔍 Navigation params:', searchQuery);

      console.log('🔍 About to navigate...');
      navigate({
        to: '/search-results',
        search: searchQuery
      });
      console.log('🔍 Navigation called!');

      return {
        success: true,
        data,
        message: `Tìm thấy ${result.data?.total || 0} mã giảm giá từ AccessTrade cho "${data.searchValue}"`
      };
    } catch (error) {
      console.error('Search error:', error);
      console.log('🔍 Error occurred, but still navigating to search results...');

      // Navigate anyway even if there's an error
      const searchQuery = {
        q: data.searchValue,
        type: data.searchType,
        category: data.category || '',
        sortBy: data.sortBy,
      };

      navigate({
        to: '/search-results',
        search: searchQuery
      });

      return {
        success: true, // Return success to prevent form error state
        data,
        message: 'Đang tìm kiếm mã giảm giá...'
      };
    }
  };

  const handleCouponDetailClick = (couponId: string) => {
    setSelectedCouponId(couponId);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedCouponId(null);
  };

  const sampleCoupons = [
    {
      title: 'Giảm 50% cho đơn hàng đầu tiên',
      description:
        'Áp dụng cho tất cả sản phẩm thời trang nam nữ, không giới hạn số lượng',
      discount: '50%',
      code: 'FIRST50',
      expiryDate: '31/12/2024',
      store: 'Shopee Mall',
      category: 'Thời trang',
    },
    {
      title: 'Freeship toàn quốc',
      description: 'Miễn phí vận chuyển cho đơn hàng từ 99k trên toàn quốc',
      discount: 'Freeship',
      code: 'FREESHIP99',
      expiryDate: '25/12/2024',
      store: 'Shopee Express',
      category: 'Vận chuyển',
    },
    {
      title: 'Giảm 100k cho đơn từ 500k',
      description:
        'Giảm ngay 100.000đ cho đơn hàng từ 500.000đ, áp dụng cho điện tử',
      discount: '100k',
      code: 'TECH100',
      expiryDate: '20/12/2024',
      store: 'TechZone',
      category: 'Điện tử',
      isExpired: true,
    },
  ];

  return (
    <div className='min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50'>
      {/* Hero Section */}
      <div className='bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 text-white relative overflow-hidden'>
        {/* Background decoration */}
        <div className='absolute inset-0 bg-gradient-to-r from-purple-600/20 via-blue-600/20 to-indigo-600/20'></div>
        <div className='absolute top-0 left-0 w-full h-full'>
          <div className='absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl'></div>
          <div className='absolute top-32 right-20 w-32 h-32 bg-white/5 rounded-full blur-2xl'></div>
          <div className='absolute bottom-20 left-1/3 w-24 h-24 bg-white/10 rounded-full blur-xl'></div>
        </div>

        <div className='container mx-auto px-4 py-20 relative z-10'>
          <div className='text-center max-w-4xl mx-auto animate-fade-in'>
            <h1 className='text-6xl font-display font-bold mb-6 text-balance'>
              🎫 Coupon Finder
            </h1>
            <p className='text-xl opacity-95 mb-12 text-balance leading-relaxed'>
              Nền tảng tìm kiếm mã giảm giá và so sánh sản phẩm thông minh nhất Việt Nam
            </p>

            {/* Search Form */}
            <div className='glass shadow-strong animate-slide-up rounded-lg'>
              <CouponSearchForm
                onSubmit={handleCouponSearch}
                defaultValues={{
                  searchType: 'keyword',
                  sortBy: 'discount'
                }}
                className='bg-transparent border-0'
              />
            </div>

            {/* Test Navigation Button */}
            <div className='mt-4'>
              <Button
                onClick={() => {
                  console.log('🧪 Test navigation button clicked');
                  navigate({
                    to: '/search-results',
                    search: { q: 'test', type: 'keyword', category: '', sortBy: 'discount' }
                  });
                }}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                🧪 Test Navigation
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className='container mx-auto px-4 py-20'>
        <div className='text-center mb-16 animate-fade-in'>
          <h2 className='text-4xl font-display font-bold mb-6 text-balance'>
            Tính năng nổi bật
          </h2>
          <p className='text-muted-foreground text-xl max-w-2xl mx-auto text-balance'>
            Tất cả những gì bạn cần để tiết kiệm tối đa khi mua sắm online
          </p>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
          <Card className='text-center group hover:shadow-strong hover:-translate-y-2 transition-all duration-300 animate-slide-up'>
            <CardHeader className='pb-8'>
              <div className='text-5xl mb-6 group-hover:scale-110 transition-transform duration-300'>🔍</div>
              <CardTitle className='text-xl mb-3'>Tìm Mã Giảm Giá</CardTitle>
              <CardDescription className='text-base leading-relaxed'>
                Tìm kiếm mã giảm giá Shopee thông minh với URL sản phẩm, tiết kiệm thời gian và tiền bạc
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className='text-center group hover:shadow-strong hover:-translate-y-2 transition-all duration-300 animate-slide-up [animation-delay:200ms]'>
            <CardHeader className='pb-8'>
              <div className='text-5xl mb-6 group-hover:scale-110 transition-transform duration-300'>⚖️</div>
              <CardTitle className='text-xl mb-3'>So Sánh Sản Phẩm</CardTitle>
              <CardDescription className='text-base leading-relaxed'>
                So sánh giá và tính năng của nhiều sản phẩm cùng lúc để đưa ra quyết định tốt nhất
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className='text-center group hover:shadow-strong hover:-translate-y-2 transition-all duration-300 animate-slide-up [animation-delay:400ms]'>
            <CardHeader className='pb-8'>
              <div className='text-5xl mb-6 group-hover:scale-110 transition-transform duration-300'>🏆</div>
              <CardTitle className='text-xl mb-3'>Top Deals</CardTitle>
              <CardDescription className='text-base leading-relaxed'>
                Khám phá các deal hot và sản phẩm bán chạy nhất được cập nhật liên tục
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>

      {/* Featured Coupons Section */}
      <div className='bg-gradient-secondary relative overflow-hidden'>
        {/* Background decoration */}
        <div className='absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full blur-3xl'></div>
        <div className='absolute bottom-0 left-0 w-48 h-48 bg-primary/10 rounded-full blur-2xl'></div>

        <div className='container mx-auto px-4 py-20 relative z-10'>
          <div className='text-center mb-16 animate-fade-in'>
            <h2 className='text-4xl font-display font-bold mb-6 text-balance'>
              Mã giảm giá hot 🔥
            </h2>
            <p className='text-muted-foreground text-xl max-w-2xl mx-auto text-balance'>
              Những mã giảm giá được săn đón nhất hiện tại, cập nhật liên tục từ AccessTrade
            </p>
          </div>

          {/* Real Coupon List với AccessTrade API */}
          <CouponList
            sortBy="popularity"
            pageSize={6}
            showFilters={false}
            showPagination={false}
            className="animate-fade-in"
          />
        </div>
      </div>

      {/* Status Section */}
      <div className='container mx-auto px-4 py-12'>
        <Card className='bg-gradient-to-r from-green-50 to-emerald-50 border-green-200/50 animate-fade-in'>
          <CardContent className='pt-8'>
            <div className='text-center space-y-4'>
              <div className='flex items-center justify-center gap-3 flex-wrap'>
                <Badge variant='success' className='text-sm'>✅ Tailwind CSS v4</Badge>
                <Badge variant='success' className='text-sm'>✅ Shadcn/ui</Badge>
                <Badge variant='success' className='text-sm'>✅ TanStack Start</Badge>
                <Badge variant='success' className='text-sm'>✅ Modern UI</Badge>
              </div>
              <p className='text-muted-foreground text-base'>
                Setup hoàn tất: Custom theme, Modern components, TypeScript, Purple-Blue gradient design
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Coupon Detail Modal */}
      <CouponDetailModal
        couponId={selectedCouponId}
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />
    </div>
  );
}
